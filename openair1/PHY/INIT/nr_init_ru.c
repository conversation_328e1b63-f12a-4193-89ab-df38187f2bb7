/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/* 中文说明：本文件是NR射频单元（RU - Radio Unit）初始化的核心文件
 * 主要功能：
 * 1. 初始化RU的物理层参数和数据结构
 * 2. 配置RU的射频接口和信号缓冲区
 * 3. 分配和管理RU所需的内存缓冲区
 * 4. 处理不同RU接口类型（本地RF、远程IF5等）的初始化
 *
 * 数据流向：
 * 输入数据来源：
 * - RU配置参数（来自配置文件或gNB配置）
 * - 帧参数配置（NR_DL_FRAME_PARMS）
 * - 接口类型配置（本地RF、远程IF5等）
 *
 * 输出数据流向：
 * - 初始化完成的RU结构体供射频处理使用
 * - 配置好的信号缓冲区供OFDM调制解调使用
 * - 初始化的PRACH缓冲区供随机接入处理使用
 *
 * 调用关系：
 * - 被RU初始化流程调用
 * - 与gNB初始化协同工作
 * - 为后续的射频信号处理做准备
 */

#include "phy_init.h"                                    // PHY初始化头文件
#include "PHY/phy_extern.h"                             // PHY外部变量定义
#include "SCHED/sched_common.h"                         // 调度通用定义
#include "common/utils/LOG/vcd_signal_dumper.h"        // VCD信号转储工具
#include "assertions.h"                                  // 断言宏定义
#include <math.h>                                        // 数学函数库
#include "openair1/PHY/defs_RU.h"                       // RU定义头文件

void init_prach_ru_list(RU_t *ru);  // PRACH RU列表初始化函数声明

/* 中文说明：nr_phy_init_RU - NR射频单元物理层初始化函数
 *
 * 函数作用：
 * - 初始化RU的物理层信号缓冲区和数据结构
 * - 配置天线、波束成形等射频参数
 * - 根据接口类型分配相应的内存缓冲区
 * - 初始化PRACH接收处理相关资源
 *
 * 输入数据：
 * - ru：待初始化的RU实例指针
 * - ru->nr_frame_parms：NR帧参数配置
 * - ru->config：NFAPI配置参数
 * - ru->if_south：南向接口类型（本地RF、远程IF5等）
 *
 * 数据处理：
 * - 解析配置参数（天线数量、波束成形配置等）
 * - 计算发送/接收流数量
 * - 分配时域和频域信号缓冲区
 * - 配置PRACH接收缓冲区
 *
 * 输出数据流向：
 * - 配置好的RU结构体供射频处理使用
 * - 分配的缓冲区供OFDM调制解调使用
 * - 初始化的PRACH缓冲区供随机接入检测使用
 *
 * 调用关系：
 * - 被RU初始化流程调用
 * - 调用init_prach_ru_list初始化PRACH列表
 * - 与gNB初始化协同工作
 */
void nr_phy_init_RU(RU_t *ru)
{
  // 获取NR帧参数指针
  NR_DL_FRAME_PARMS *fp = ru->nr_frame_parms;

  LOG_D(PHY, "Initializing RU signal buffers (if_south %s) nb_tx %d, nb_rx %d\n",
        ru_if_types[ru->if_south], ru->nb_tx, ru->nb_rx);

  // 获取配置参数
  nfapi_nr_config_request_scf_t *cfg = &ru->config;

  // 计算逻辑天线数量（取所有gNB中的最大值）
  ru->nb_log_antennas = 0;
  for (int n = 0; n < ru->num_gNB; n++) {
    if (cfg->carrier_config.num_tx_ant.value > ru->nb_log_antennas)
      ru->nb_log_antennas = cfg->carrier_config.num_tx_ant.value;
  }

  // 从gNB[0]复制配置到RU，假设共享RU的所有gNB实例使用相同配置
  // （至少RU需要的部分，如numerology和PRACH）

  // 验证逻辑天线数量的有效性
  AssertFatal(ru->nb_log_antennas > 0 && ru->nb_log_antennas < 13,
              "ru->nb_log_antennas %d ! \n",ru->nb_log_antennas);

  // 配置模拟波束成形参数
  nfapi_nr_analog_beamforming_ve_t *analog_config = &cfg->analog_beamforming_ve;
  ru->num_beams_period = analog_config->analog_bf_vendor_ext.value ?
                         analog_config->num_beams_period_vendor_ext.value : 1;

  // 计算发送和接收流数量（天线数×波束周期数）
  int nb_tx_streams = ru->nb_tx * ru->num_beams_period;
  int nb_rx_streams = ru->nb_rx * ru->num_beams_period;
  LOG_I(NR_PHY, "nb_tx_streams %d, nb_rx_streams %d, num_Beams_period %d\n",
        nb_tx_streams, nb_rx_streams, ru->num_beams_period);

  // 分配波束ID数组
  ru->common.beam_id = malloc16_clear(ru->num_beams_period * sizeof(int*));
  for(int i = 0; i < ru->num_beams_period; i++)
    // 为每个波束周期分配符号×时隙的波束ID存储空间
    ru->common.beam_id[i] = malloc16_clear(fp->symbols_per_slot * fp->slots_per_frame * sizeof(int));

  // 根据南向接口类型分配时域信号缓冲区
  if (ru->if_south <= REMOTE_IF5) { // REMOTE_IF5或LOCAL_RF接口，需要分配时域信号内存
    // 分配时域信号缓冲区指针数组
    ru->common.txdata = (int32_t**)malloc16(nb_tx_streams * sizeof(int32_t*));  // 发送数据
    ru->common.rxdata = (int32_t**)malloc16(nb_rx_streams * sizeof(int32_t*));  // 接收数据

    // 为每个发送流分配时域缓冲区
    for (int i = 0; i < nb_tx_streams; i++) {
      // 分配10个子帧的I/Q发送信号数据（时域），包含子帧扩展
      ru->common.txdata[i] = (int32_t*)malloc16_clear((ru->sf_extension + fp->samples_per_frame) * sizeof(int32_t));
      LOG_D(PHY,
            "[INIT] common.txdata[%d] = %p (%lu bytes,sf_extension %d)\n",
            i,
            ru->common.txdata[i],
            (ru->sf_extension + fp->samples_per_frame) * sizeof(int32_t),
            ru->sf_extension);

      // 调整指针位置，跳过子帧扩展部分，指向实际数据起始位置
      ru->common.txdata[i] = &ru->common.txdata[i][ru->sf_extension];

      LOG_D(PHY, "[INIT] common.txdata[%d] = %p \n", i, ru->common.txdata[i]);
    }

    // 为每个接收流分配时域缓冲区
    for (int i = 0; i < nb_rx_streams; i++) {
      // 分配一帧的接收信号数据缓冲区
      ru->common.rxdata[i] = (int32_t*)malloc16_clear(fp->samples_per_frame * sizeof(int32_t));
    }
  } // IF5或本地RF接口处理结束
  else {
    // 其他接口类型不需要时域缓冲区
    ru->common.txdata = (int32_t**)NULL;
    ru->common.rxdata = (int32_t**)NULL;
  }
  // 如果RU功能不是NGFI_RRU_IF5，需要进行RX/TX RU处理
  if (ru->function != NGFI_RRU_IF5) {
    LOG_D(PHY, "nb_tx %d\n", ru->nb_tx);

    // 分配7.5kHz接收数据缓冲区（用于特殊子载波间隔处理）
    ru->common.rxdata_7_5kHz = (int32_t**)malloc16(ru->nb_rx*sizeof(int32_t*) );
    for (int i = 0; i < ru->nb_rx; i++) {
      // 分配2个子帧×2倍采样的缓冲区
      ru->common.rxdata_7_5kHz[i] = (int32_t*)malloc16_clear( 2*fp->samples_per_subframe*2*sizeof(int32_t) );
      LOG_D(PHY, "rxdata_7_5kHz[%d] %p for RU %d\n", i, ru->common.rxdata_7_5kHz[i], ru->idx);
    }

    // 分配预编码输入缓冲区（TX）
    ru->common.txdataF = (int32_t **)malloc16(ru->nb_tx*sizeof(int32_t*));
    // 注：samples_per_frame_wCP是带CP的帧样本数
    for(int i = 0; i < ru->nb_tx; ++i)
      ru->common.txdataF[i] = (int32_t*)malloc16_clear(fp->samples_per_frame_wCP * sizeof(int32_t));

    // 分配IFFT输入缓冲区（TX，波束成形后）
    ru->common.txdataF_BF = (int32_t **)malloc16(nb_tx_streams * sizeof(int32_t*));
    LOG_D(PHY, "[INIT] common.txdata_BF= %p (%lu bytes)\n", ru->common.txdataF_BF, nb_tx_streams * sizeof(int32_t *));
    for (int i = 0; i < nb_tx_streams; i++) {
      // 分配一个子帧带CP的频域数据缓冲区
      ru->common.txdataF_BF[i] = (int32_t*)malloc16_clear(fp->samples_per_subframe_wCP * sizeof(int32_t));
      LOG_D(PHY, "txdataF_BF[%d] %p for RU %d\n", i, ru->common.txdataF_BF[i], ru->idx);
    }

    // 分配FFT输出缓冲区（RX）
    ru->common.rxdataF = (int32_t**)malloc16(nb_rx_streams * sizeof(int32_t*));
    for (int i = 0; i < nb_rx_streams; i++) {
      // 分配4个时隙的I/Q信号数据（频域）
      int size = RU_RX_SLOT_DEPTH * fp->symbols_per_slot * fp->ofdm_symbol_size;
      ru->common.rxdataF[i] = (int32_t*)malloc16_clear(sizeof(**ru->common.rxdataF) * size);
      LOG_D(PHY, "rxdataF[%d] %p for RU %d\n", i, ru->common.rxdataF[i], ru->idx);
    }

    /* 数组X的元素个数计算方法：sizeof(X) / sizeof(X[0]) */
    // 注释掉的断言检查接收天线数量是否超出限制
    //    AssertFatal(ru->nb_rx <= sizeof(ru->prach_rxsigF) / sizeof(ru->prach_rxsigF[0]),
    //		"nb_antennas_rx too large");

    // 为所有可能的PRACH时机分配接收信号频域缓冲区
    for (int j = 0; j < NUMBER_OF_NR_RU_PRACH_OCCASIONS_MAX; j++) {
      // 为每个PRACH时机分配接收流指针数组
      ru->prach_rxsigF[j] = (int16_t**)malloc(nb_rx_streams * sizeof(int16_t*));

      // 为每个接收流分配PRACH频域信号缓冲区
      for (int i = 0; i < nb_rx_streams; i++) {
        // PRACH FFT的最大尺寸是4×98304（16×24576）
        // 乘以2是因为复数（实部+虚部）
        ru->prach_rxsigF[j][i] = (int16_t*)malloc16_clear(4 * 98304 * 2 * sizeof(int16_t));
        LOG_D(PHY,"[INIT] prach_vars->rxsigF[%d] = %p\n", i, ru->prach_rxsigF[j][i]);
      }
    }

    // 检查gNB实例数量是否超出最大限制
    AssertFatal(ru->num_gNB <= NUMBER_OF_gNB_MAX, "gNB instances %d > %d\n", ru->num_gNB,NUMBER_OF_gNB_MAX);

    LOG_D(PHY, "[INIT] %s() ru->num_gNB:%d \n", __FUNCTION__, ru->num_gNB);
  } // 非IF5接口处理结束

  // 初始化PRACH RU列表
  init_prach_ru_list(ru);
}

/* 中文说明：nr_phy_free_RU - 释放NR射频单元物理层资源
 *
 * 函数作用：
 * - 释放nr_phy_init_RU中分配的所有内存资源
 * - 清理时域和频域信号缓冲区
 * - 释放PRACH相关缓冲区
 * - 确保RU关闭时没有内存泄漏
 *
 * 输入数据：
 * - ru：待释放资源的RU实例指针
 *
 * 数据处理：
 * - 按照与初始化相反的顺序释放内存
 * - 根据接口类型释放相应的缓冲区
 * - 更新gNB中的RU计数
 *
 * 输出数据流向：
 * - 释放的内存返回给系统内存池
 * - 清理后的RU结构体不再可用
 *
 * 调用关系：
 * - 被RU关闭或重配置流程调用
 * - 与nr_phy_init_RU成对使用
 */
void nr_phy_free_RU(RU_t *ru)
{
  LOG_D(PHY, "Freeing RU signal buffers (if_south %s) nb_tx %d\n", ru_if_types[ru->if_south], ru->nb_tx);

  // 计算发送和接收流数量（与初始化时保持一致）
  int nb_tx_streams = ru->nb_tx * ru->num_beams_period;
  int nb_rx_streams = ru->nb_rx * ru->num_beams_period;

  // 如果是REMOTE_IF5或LOCAL_RF接口，释放时域信号内存
  if (ru->if_south <= REMOTE_IF5) {
    // 技巧：撤销分配时的操作，恢复原始指针位置
    for (int i = 0; i < nb_tx_streams; i++) {
      // 恢复到分配时的原始指针位置（减去子帧扩展偏移）
      int32_t *p = &ru->common.txdata[i][-ru->sf_extension];
      free_and_zero(p);
    }
    free_and_zero(ru->common.txdata);

    // 释放接收数据缓冲区
    for (int i = 0; i < nb_rx_streams; i++)
      free_and_zero(ru->common.rxdata[i]);
    free_and_zero(ru->common.rxdata);
  } // 其他接口类型无需释放时域缓冲区

  // 如果RU功能不是NGFI_RRU_IF5，释放RX/TX处理相关缓冲区
  if (ru->function != NGFI_RRU_IF5) {
    // 释放7.5kHz接收数据缓冲区
    for (int i = 0; i < ru->nb_rx; i++)
      free_and_zero(ru->common.rxdata_7_5kHz[i]);
    free_and_zero(ru->common.rxdata_7_5kHz);

    // 释放波束成形输入缓冲区（TX）
    for (int i = 0; i < ru->nb_tx; i++)
      free_and_zero(ru->common.txdataF[i]);
    free_and_zero(ru->common.txdataF);

    // 释放IFFT输入缓冲区（TX）
    for (int i = 0; i < nb_tx_streams; i++)
      free_and_zero(ru->common.txdataF_BF[i]);
    free_and_zero(ru->common.txdataF_BF);

    // 释放FFT输出缓冲区（RX）
    for (int i = 0; i < nb_rx_streams; i++)
      free_and_zero(ru->common.rxdataF[i]);
    free_and_zero(ru->common.rxdataF);

    // 释放PRACH接收信号频域缓冲区
    for (int j = 0; j < NUMBER_OF_NR_RU_PRACH_OCCASIONS_MAX; j++) {
      for (int i = 0; i < nb_rx_streams; i++)
        free_and_zero(ru->prach_rxsigF[j][i]);
      free_and_zero(ru->prach_rxsigF[j]);
    }

    // 释放波束ID数组
    for(int i = 0; i < ru->num_beams_period; ++i)
      free_and_zero(ru->common.beam_id[i]);
    free_and_zero(ru->common.beam_id);
  }

  // 更新gNB中的RU计数
  PHY_VARS_gNB *gNB0 = ru->gNB_list[0];
  gNB0->num_RU--;
  DevAssert(gNB0->num_RU >= 0);  // 确保RU计数不为负数
}
