/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/* 中文说明：本文件是NR（5G New Radio）物理层初始化的核心文件
 * 主要功能：
 * 1. 初始化gNB（5G基站）的物理层参数和数据结构
 * 2. 配置射频单元（RU）和用户设备（UE）的物理层参数
 * 3. 分配和管理物理层所需的内存缓冲区
 * 4. 初始化各种物理层算法所需的查找表和参数
 *
 * 数据流向：
 * 输入数据来源：
 * - 配置文件参数（nfapi_nr_config_request_scf_t）
 * - 系统初始化参数（帧结构、天线配置等）
 * - 上层协议栈的配置请求
 *
 * 输出数据流向：
 * - 初始化完成的PHY_VARS_gNB结构体，供物理层处理函数使用
 * - 配置好的内存缓冲区，供信号处理算法使用
 * - 初始化的查找表，供编码解码算法使用
 *
 * 调用关系：
 * - 被main函数或系统初始化函数调用
 * - 调用各种子模块的初始化函数
 * - 为后续的物理层信号处理做准备
 */

#include "executables/softmodem-common.h"        // 软调制解调器通用定义
#include "executables/nr-softmodem-common.h"     // NR软调制解调器通用定义
#include "common/utils/nr/nr_common.h"           // NR通用工具函数
#include "common/ran_context.h"                  // RAN（无线接入网）上下文
#include "PHY/defs_gNB.h"                       // gNB物理层定义
#include "PHY/NR_REFSIG/nr_refsig.h"            // NR参考信号定义
#include "PHY/INIT/nr_phy_init.h"               // NR物理层初始化头文件
#include "PHY/CODING/nrLDPC_coding/nrLDPC_coding_interface.h"  // NR LDPC编码接口
#include "PHY/CODING/nrPolar_tools/nr_polar_pbch_defs.h"       // NR极化码PBCH定义
#include "PHY/NR_TRANSPORT/nr_transport_proto.h"               // NR传输层协议
#include "PHY/NR_TRANSPORT/nr_transport_common_proto.h"        // NR传输层通用协议
#include "PHY/NR_ESTIMATION/nr_ul_estimation.h"                // NR上行链路估计
#include "openair1/PHY/MODULATION/nr_modulation.h"             // NR调制解调
#include "openair1/PHY/defs_RU.h"                              // 射频单元定义
#include "openair1/PHY/CODING/nrLDPC_extern.h"                 // NR LDPC外部接口
#include "assertions.h"                                         // 断言宏定义
#include <math.h>                                               // 数学函数库
#include <complex.h>                                            // 复数运算库
#include "PHY/NR_TRANSPORT/nr_ulsch.h"                          // NR上行共享信道
#include "PHY/NR_REFSIG/nr_refsig.h"                           // NR参考信号（重复包含）
#include "SCHED_NR/fapi_nr_l1.h"                               // NR FAPI L1接口
#include "PHY/NR_REFSIG/ul_ref_seq_nr.h"                       // NR上行参考序列
#include <string.h>                                             // 字符串处理函数

/* 中文说明：l1_north_init_gNB - 初始化gNB的L1北向接口
 *
 * 函数作用：
 * - 初始化gNB的L1层北向接口模块
 * - 设置PHY层配置请求和调度响应的回调函数
 * - 建立L1层与上层协议栈的通信接口
 *
 * 输入数据：
 * - RC.nb_nr_L1_inst：NR L1实例数量（来自全局运行时配置）
 * - RC.gNB：gNB实例数组指针（来自全局运行时配置）
 *
 * 数据处理：
 * - 为每个gNB实例初始化NR_IF_Module接口
 * - 安装PHY配置请求回调函数（nr_phy_config_request）
 * - 安装调度响应回调函数（nr_schedule_response）
 *
 * 输出数据流向：
 * - 配置好的接口模块供上层MAC/RRC使用
 * - 回调函数供FAPI接口调用
 *
 * 调用关系：
 * - 被系统初始化函数调用
 * - 调用NR_IF_Module_init进行接口模块初始化
 * - 设置的回调函数将被FAPI层调用
 */
int l1_north_init_gNB()
{
  // 检查NR L1实例数量是否有效
  AssertFatal(RC.nb_nr_L1_inst > 0, "Failed to init PHY callbacks: nb_nr_L1_inst = %d\n", RC.nb_nr_L1_inst);
  // 检查gNB实例指针是否有效
  AssertFatal(RC.gNB != NULL, "Failed to init PHY callbacks: RC.gNB is null\n");

  // 遍历所有NR L1实例，为每个gNB初始化北向接口
  for (uint8_t i = 0; i < RC.nb_nr_L1_inst; i++) {

    // 初始化NR接口模块，返回接口实例ID
    if ((RC.gNB[i]->if_inst = NR_IF_Module_init(i)) < 0) {
      LOG_E(NR_PHY, "Error: Failed to initialize NR_IF_Module for gNB[%d]\n", i);
      return -1;
    }

    LOG_D(NR_PHY, "RC.gNB[%d]: installing callbacks\n", i);
    // 安装PHY配置请求回调函数，用于处理来自上层的配置请求
    RC.gNB[i]->if_inst->NR_PHY_config_req = nr_phy_config_request;
    // 安装调度响应回调函数，用于处理来自MAC层的调度指令
    RC.gNB[i]->if_inst->NR_Schedule_response = nr_schedule_response;
  }

  return 0;
}

/* 中文说明：get_phy_stats - 获取或创建UE的PHY统计信息结构
 *
 * 函数作用：
 * - 根据RNTI查找现有的UE PHY统计信息
 * - 如果不存在则创建新的统计信息结构
 * - 管理gNB中UE的PHY统计信息资源
 *
 * 输入数据：
 * - gNB：gNB实例指针（包含所有UE的统计信息数组）
 * - rnti：无线网络临时标识符，用于标识特定UE
 *
 * 数据处理：
 * - 遍历gNB->phy_stats数组查找匹配的RNTI
 * - 如果找到活跃的匹配项，直接返回
 * - 如果未找到，分配第一个空闲的统计信息结构
 * - 初始化新分配的统计信息结构
 *
 * 输出数据流向：
 * - 返回的统计信息结构供PHY层各模块使用
 * - 用于记录DLSCH、ULSCH、UCI等的统计数据
 * - 统计数据可用于性能监控和调试
 *
 * 调用关系：
 * - 被PHY层处理函数调用（如DLSCH/ULSCH处理）
 * - 返回的结构被用于更新各种PHY统计信息
 */
NR_gNB_PHY_STATS_t *get_phy_stats(PHY_VARS_gNB *gNB, uint16_t rnti)
{
  NR_gNB_PHY_STATS_t *stats;
  int first_free = -1;

  // 遍历所有可能的UE统计信息槽位
  for (int i = 0; i < MAX_MOBILES_PER_GNB; i++) {
    stats = &gNB->phy_stats[i];
    // 如果找到活跃且RNTI匹配的统计信息，直接返回
    if (stats->active && stats->rnti == rnti)
      return stats;
    // 记录第一个空闲槽位的索引
    else if (!stats->active && first_free == -1)
      first_free = i;
  }

  // 如果没有空闲槽位，返回NULL
  if (first_free < 0)
    return NULL;

  // 创建新的统计信息结构
  stats = &gNB->phy_stats[first_free];
  stats->active = true;           // 标记为活跃
  stats->rnti = rnti;            // 设置RNTI
  // 清零所有统计计数器
  memset(&stats->dlsch_stats, 0, sizeof(stats->dlsch_stats));  // 下行共享信道统计
  memset(&stats->ulsch_stats, 0, sizeof(stats->ulsch_stats));  // 上行共享信道统计
  memset(&stats->uci_stats, 0, sizeof(stats->uci_stats));      // 上行控制信息统计
  return stats;
}

/* 中文说明：reset_active_stats - 重置不活跃UE的PHY统计信息
 *
 * 函数作用：
 * - 检查所有UE的PHY统计信息活跃状态
 * - 将长时间不活跃的UE统计信息标记为非活跃
 * - 释放不再使用的统计信息资源
 *
 * 输入数据：
 * - gNB：gNB实例指针（包含所有UE的统计信息）
 * - frame：当前帧号（用于计算UE不活跃时间）
 *
 * 数据处理：
 * - 遍历所有活跃的UE统计信息
 * - 计算每个UE自上次活跃以来经过的帧数
 * - 如果超过阈值（NUMBER_FRAMES_PHY_UE_INACTIVE），标记为非活跃
 *
 * 输出数据流向：
 * - 更新gNB->phy_stats数组中的active标志
 * - 释放的统计信息槽位可供新UE使用
 *
 * 调用关系：
 * - 被PHY层主处理循环定期调用
 * - 用于资源管理和内存清理
 */
void reset_active_stats(PHY_VARS_gNB *gNB, int frame)
{
  // 遍历所有UE统计信息槽位，检查并重置不活跃的UE统计
  for (int i = 0; i < MAX_MOBILES_PER_GNB; i++) {
    NR_gNB_PHY_STATS_t *stats = &gNB->phy_stats[i];
    // 如果UE统计信息当前活跃，检查是否超过不活跃阈值
    // 使用模1024运算处理帧号回绕情况
    if (stats->active && (((frame - stats->frame + 1024) % 1024) > NUMBER_FRAMES_PHY_UE_INACTIVE))
      stats->active = false;  // 标记为非活跃，释放该统计信息槽位
  }
}

/* 中文说明：phy_init_nr_gNB - NR gNB物理层主初始化函数
 *
 * 函数作用：
 * - 初始化gNB物理层的所有核心组件和数据结构
 * - 配置天线、波束成形、MIMO等射频参数
 * - 分配和初始化各种信号处理缓冲区
 * - 加载和初始化各种算法库和查找表
 *
 * 输入数据：
 * - gNB：gNB实例指针，包含配置信息和待初始化的数据结构
 * - gNB->gNB_config：来自上层的NFAPI配置请求
 * - gNB->frame_parms：帧参数配置
 *
 * 数据处理：
 * - 解析配置参数（天线数量、波束成形配置等）
 * - 分配内存缓冲区（发送/接收数据缓冲区）
 * - 初始化各种算法库（LDPC、CRC、调制等）
 * - 配置PRACH、PUSCH等物理信道参数
 *
 * 输出数据流向：
 * - 完全初始化的gNB结构体供物理层处理使用
 * - 配置好的缓冲区供信号处理算法使用
 * - 初始化的查找表供编码解码使用
 *
 * 调用关系：
 * - 被系统初始化流程调用
 * - 调用各种子模块初始化函数
 * - 为后续的物理层信号处理做准备
 */
void phy_init_nr_gNB(PHY_VARS_gNB *gNB)
{
  // 创建快捷方式指针，便于访问各个子结构
  NR_DL_FRAME_PARMS *const fp       = &gNB->frame_parms;        // 帧参数
  nfapi_nr_config_request_scf_t *cfg = &gNB->gNB_config;        // NFAPI配置
  NR_gNB_COMMON *const common_vars  = &gNB->common_vars;        // 通用变量
  NR_gNB_PRACH *const prach_vars   = &gNB->prach_vars;         // PRACH变量

  // 配置模拟波束成形参数
  common_vars->analog_bf = cfg->analog_beamforming_ve.analog_bf_vendor_ext.value;
  LOG_I(PHY, "L1 configured with%s analog beamforming\n", common_vars->analog_bf ? "" : "out");
  if (common_vars->analog_bf) {
    // 如果启用模拟波束成形，设置并发波束数量
    common_vars->num_beams_period = cfg->analog_beamforming_ve.num_beams_period_vendor_ext.value;
    LOG_I(PHY, "Max number of concurrent beams: %d\n", common_vars->num_beams_period);
  } else
    common_vars->num_beams_period = 1;  // 不使用波束成形时设为1

  // 获取天线配置参数
  int Ptx = cfg->carrier_config.num_tx_ant.value;  // 发送天线数量
  int Prx = cfg->carrier_config.num_rx_ant.value;  // 接收天线数量
  int max_ul_mimo_layers = 4;                      // 最大上行MIMO层数

  // 验证天线配置的有效性
  AssertFatal(Ptx > 0 && Ptx < 9,"Ptx %d is not supported\n", Ptx);
  AssertFatal(Prx > 0 && Prx < 9,"Prx %d is not supported\n", Prx);
  LOG_D(PHY, "[gNB %d]About to wait for gNB to be configured\n", gNB->Mod_id);

  // 等待gNB配置完成
  while(gNB->configured == 0)
    usleep(10000);

  // 加载DFT（离散傅里叶变换）库，用于OFDM调制解调
  load_dftslib();

  // 初始化各种算法库和查找表
  crcTableInit();      // 初始化CRC校验表
  init_byte2m128i();   // 初始化字节到128位整数转换表
  init_byte2bit16();   // 初始化字节到16位转换表
  init_pucch2_luts();  // 初始化PUCCH格式2查找表

  // 初始化频域均衡数组，用于PUSCH的变换预编码
  nr_init_fde();

  // 加载NR LDPC编码接口库
  int ret_loader = load_nrLDPC_coding_interface(NULL, &gNB->nrLDPC_coding_interface);
  AssertFatal(ret_loader == 0, "error loading LDPC library\n");

  // 设置最大PDSCH数量（等于最大支持的UE数量）
  gNB->max_nb_pdsch = MAX_MOBILES_PER_GNB;

  // 初始化延迟补偿表，用于信道估计和均衡
  init_delay_table(fp->ofdm_symbol_size, MAX_DELAY_COMP, NR_MAX_OFDM_SYMBOL_SIZE, fp->delay_table);
  init_delay_table(128, MAX_DELAY_COMP, 128, fp->delay_table128);

  // 初始化PUCCH相关参数
  gNB->bad_pucch = 0;  // 坏PUCCH计数器清零

  // 设置发送功率放大器参数
  if (gNB->TX_AMP == 0)
    gNB->TX_AMP = AMP;  // 如果未设置，使用默认放大倍数

  // 生成调制查找表（QPSK、16QAM、64QAM、256QAM等）
  nr_generate_modulation_table();

  // 初始化PBCH交织器，用于物理广播信道的比特交织
  nr_init_pbch_interleaver(gNB->nr_pbch_interleaver);

  // 生成上行参考信号序列，用于信道估计和同步
  generate_ul_reference_signal_sequences(SHRT_MAX);

  // 生成低PAPR（峰均功率比）类型1序列，用于启用变换预编码时的PUSCH DMRS
  generate_lowpapr_typ1_refsig_sequences(SHRT_MAX);

  // 初始化NR传输层，这对NR同步是必需的
  init_nr_transport(gNB);

  // 分配和初始化SRS（探测参考信号）信息结构数组
  gNB->nr_srs_info = (nr_srs_info_t **)malloc16_clear(gNB->max_nb_srs * sizeof(nr_srs_info_t*));
  for (int id = 0; id < gNB->max_nb_srs; id++) {
    // 为每个SRS ID分配信息结构
    gNB->nr_srs_info[id] = (nr_srs_info_t *)malloc16_clear(sizeof(nr_srs_info_t));
    // 分配SRS生成信号的天线端口数组
    gNB->nr_srs_info[id]->srs_generated_signal = malloc16_clear(MAX_NUM_NR_SRS_AP * sizeof(c16_t *));
    // 为每个天线端口分配SRS信号缓冲区
    for(int ap=0; ap<MAX_NUM_NR_SRS_AP; ap++) {
      gNB->nr_srs_info[id]->srs_generated_signal[ap] =
          malloc16_clear(fp->ofdm_symbol_size * MAX_NUM_NR_SRS_SYMBOLS * sizeof(c16_t));
    }
  }

  /* 注意：不为每个天线分配rxdataF内存，gNB从RU获取指针来复制/恢复频域内存 */
  // 分配接收数据频域缓冲区指针数组（三维：波束周期×接收天线×频域样本）
  common_vars->rxdataF = (c16_t ***)malloc16(common_vars->num_beams_period * sizeof(c16_t**));
  for (int i = 0; i < common_vars->num_beams_period; i++)
    common_vars->rxdataF[i] = (c16_t **)malloc16(Prx * sizeof(c16_t*));

  // 如果启用了模拟波束成形，分配波束ID数组
  if (cfg->analog_beamforming_ve.analog_bf_vendor_ext.value) {
    common_vars->beam_id = (int **)malloc16(common_vars->num_beams_period * sizeof(int*));
    for (int i = 0; i < common_vars->num_beams_period; i++)
      // 为每个波束周期分配符号×时隙的波束ID存储空间
      common_vars->beam_id[i] = (int*)malloc16_clear(fp->symbols_per_slot * fp->slots_per_frame * sizeof(int));
  }

  // 分配发送数据频域缓冲区（三维：波束周期×发送天线×频域样本）
  common_vars->txdataF = (c16_t ***)malloc16(common_vars->num_beams_period * sizeof(c16_t**));
  for (int i = 0; i < common_vars->num_beams_period; i++) {
    common_vars->txdataF[i] = (c16_t**)malloc16_clear(Ptx * sizeof(c16_t*));
    for (int j = 0; j < Ptx; j++)
      // 为每个发送天线分配一帧带CP的频域样本缓冲区
      common_vars->txdataF[i][j] = (c16_t*)malloc16_clear(fp->samples_per_frame_wCP * sizeof(c16_t));
  }

  // 分配调试缓冲区，用于存储调试数据（100倍帧长度）
  common_vars->debugBuff = (int32_t*)malloc16_clear(fp->samples_per_frame*sizeof(int32_t)*100);
  common_vars->debugBuff_sample_offset = 0;  // 调试缓冲区样本偏移量初始化为0

  // PRACH（物理随机接入信道）初始化
  // 分配PRACH接收信号频域缓冲区（每个接收天线一个）
  prach_vars->rxsigF = (int16_t **)malloc16_clear(Prx*sizeof(int16_t*));
  // 分配PRACH IFFT缓冲区（1024点复数，实部虚部分开存储）
  prach_vars->prach_ifft = (int32_t *)malloc16_clear(1024*2*sizeof(int32_t));

  // 初始化PRACH列表，用于管理PRACH资源
  init_prach_list(gNB);

  // 获取上行资源块数量
  int N_RB_UL = cfg->carrier_config.ul_grid_size[cfg->ssb_config.scs_common.value].value;
  // 计算缓冲区数量（接收天线数×最大上行MIMO层数）
  int n_buf = Prx*max_ul_mimo_layers;

  // 计算PUSCH资源元素数量
  int nb_re_pusch = N_RB_UL * NR_NB_SC_PER_RB;  // 总子载波数
  int nb_re_pusch2 = ceil_mod(nb_re_pusch, 16);  // 向上取整到16的倍数（SIMD优化）

  // 分配PUSCH变量数组
  gNB->pusch_vars = (NR_gNB_PUSCH *)malloc16_clear(gNB->max_nb_pusch * sizeof(NR_gNB_PUSCH));

  // 为每个PUSCH实例初始化缓冲区
  for (int ULSCH_id = 0; ULSCH_id < gNB->max_nb_pusch; ULSCH_id++) {
    NR_gNB_PUSCH *pusch = &gNB->pusch_vars[ULSCH_id];

    // 分配上行信道估计缓冲区指针数组
    pusch->ul_ch_estimates = (int32_t **)malloc16(n_buf * sizeof(int32_t *));
    // 分配PTRS（相位跟踪参考信号）每时隙相位缓冲区指针数组
    pusch->ptrs_phase_per_slot = (int32_t **)malloc16(n_buf * sizeof(int32_t *));
    // 分配接收数据频域补偿后缓冲区指针数组
    pusch->rxdataF_comp = (int32_t **)malloc16(n_buf * sizeof(int32_t *));

    // 为每个缓冲区分配实际内存
    for (int i = 0; i < n_buf; i++) {
      // 信道估计：OFDM符号大小×每时隙符号数
      pusch->ul_ch_estimates[i] = (int32_t *)malloc16_clear(sizeof(int32_t) * fp->ofdm_symbol_size * fp->symbols_per_slot);
      // PTRS相位：每时隙符号数
      pusch->ptrs_phase_per_slot[i] = (int32_t *)malloc16_clear(sizeof(int32_t) * fp->symbols_per_slot);
      // 频域补偿数据：资源元素数×每时隙符号数
      pusch->rxdataF_comp[i] = (int32_t *)malloc16_clear(sizeof(int32_t) * nb_re_pusch2 * fp->symbols_per_slot);
    }

    // 预留MIMO层处理空间（当前为空）
    for (int i = 0; i < max_ul_mimo_layers; i++) {
    }

    // 分配LLR（对数似然比）缓冲区，用于软解调输出
    // 注：公式中的6144是LTE遗留值，实际大小计算需要澄清
    pusch->llr = (int16_t *)malloc16_clear((8 * ((3 * 8 * 6144) + 12)) * sizeof(int16_t));

    // 分配每时隙有效资源元素数量数组
    pusch->ul_valid_re_per_slot = (int16_t *)malloc16_clear(sizeof(int16_t) * fp->symbols_per_slot);
  } // ulsch_id循环结束
}

/* 中文说明：phy_free_nr_gNB - 释放NR gNB物理层资源
 *
 * 函数作用：
 * - 释放phy_init_nr_gNB中分配的所有内存资源
 * - 清理各种缓冲区、查找表和数据结构
 * - 确保系统关闭时没有内存泄漏
 *
 * 输入数据：
 * - gNB：待释放资源的gNB实例指针
 *
 * 数据处理：
 * - 按照与初始化相反的顺序释放内存
 * - 释放SRS、PUSCH、PRACH等各模块的缓冲区
 * - 释放参考信号序列和查找表
 * - 重置传输层资源
 *
 * 输出数据流向：
 * - 释放的内存返回给系统内存池
 * - 清理后的gNB结构体不再可用
 *
 * 调用关系：
 * - 被系统关闭或重配置流程调用
 * - 调用各种子模块的清理函数
 * - 与phy_init_nr_gNB成对使用
 */
void phy_free_nr_gNB(PHY_VARS_gNB *gNB)
{
  // 获取天线配置参数（与初始化时保持一致）
  const int Ptx = gNB->gNB_config.carrier_config.num_tx_ant.value;
  const int Prx = gNB->gNB_config.carrier_config.num_rx_ant.value;
  const int max_ul_mimo_layers = 4; // 与phy_init_nr_gNB()中的值保持一致
  const int n_buf = Prx * max_ul_mimo_layers;

  // 释放PHY测量相关内存
  PHY_MEASUREMENTS_gNB *meas = &gNB->measurements;
  free_and_zero(meas->n0_subband_power);     // 释放子带噪声功率数组
  free_and_zero(meas->n0_subband_power_dB);  // 释放子带噪声功率dB数组

  // 释放SRS信息结构体及其相关缓冲区
  for (int id = 0; id < gNB->max_nb_srs; id++) {
    // 释放每个天线端口的SRS生成信号缓冲区
    for(int i=0; i<MAX_NUM_NR_SRS_AP; i++) {
      free_and_zero(gNB->nr_srs_info[id]->srs_generated_signal[i]);
    }
    // 释放SRS生成信号指针数组
    free_and_zero(gNB->nr_srs_info[id]->srs_generated_signal);
    // 释放SRS信息结构体
    free_and_zero(gNB->nr_srs_info[id]);
  }
  // 释放SRS信息指针数组
  free_and_zero(gNB->nr_srs_info);

  // 释放上行参考信号序列
  free_ul_reference_signal_sequences();
  // 释放gNB低PAPR序列
  free_gnb_lowpapr_sequences();

  // 重置NR传输层资源
  reset_nr_transport(gNB);

  // 释放通用变量结构中的缓冲区
  NR_gNB_COMMON * common_vars = &gNB->common_vars;

  // 释放波束相关的缓冲区
  for (int j = 0; j < common_vars->num_beams_period; j++) {
    // 释放波束ID数组（如果存在）
    if (common_vars->beam_id)
      free_and_zero(common_vars->beam_id[j]);
    // 释放每个发送天线的频域数据缓冲区
    for (int i = 0; i < Ptx; i++) {
      free_and_zero(common_vars->txdataF[j][i]);
    }
    // 释放发送天线指针数组
    free_and_zero(common_vars->txdataF[j]);
  }

  /* 注意：不释放每天线的txdataF/rxdataF，因为gNB获取的是RU的指针，
   * RU会负责释放这些内存 */
  // 释放接收数据频域指针数组
  for (int j = 0; j < common_vars->num_beams_period; j++)
    free_and_zero(common_vars->rxdataF[j]);
  free_and_zero(common_vars->txdataF);   // 释放发送数据频域主指针数组
  free_and_zero(common_vars->rxdataF);   // 释放接收数据频域主指针数组
  free_and_zero(common_vars->beam_id);   // 释放波束ID主指针数组

  // 释放调试缓冲区
  free_and_zero(common_vars->debugBuff);

  // 释放PRACH相关缓冲区
  NR_gNB_PRACH* prach_vars = &gNB->prach_vars;
  free_and_zero(prach_vars->rxsigF);      // 释放PRACH接收信号频域缓冲区
  free_and_zero(prach_vars->prach_ifft);  // 释放PRACH IFFT缓冲区

  // 释放PUSCH相关缓冲区
  for (int ULSCH_id = 0; ULSCH_id < gNB->max_nb_pusch; ULSCH_id++) {
    NR_gNB_PUSCH *pusch_vars = &gNB->pusch_vars[ULSCH_id];
    // 释放每个缓冲区的实际内存
    for (int i = 0; i < n_buf; i++) {
      free_and_zero(pusch_vars->ul_ch_estimates[i]);      // 上行信道估计
      free_and_zero(pusch_vars->ptrs_phase_per_slot[i]);  // PTRS相位
      free_and_zero(pusch_vars->rxdataF_comp[i]);         // 频域补偿数据
    }
    // 释放指针数组
    free_and_zero(pusch_vars->ul_ch_estimates);
    free_and_zero(pusch_vars->ptrs_phase_per_slot);
    free_and_zero(pusch_vars->ul_valid_re_per_slot);
    free_and_zero(pusch_vars->rxdataF_comp);

    // 释放LLR缓冲区
    free_and_zero(pusch_vars->llr);
  } // ULSCH_id循环结束

  // 释放PUSCH变量数组
  free(gNB->pusch_vars);

  // 释放NR LDPC编码接口
  free_nrLDPC_coding_interface(&gNB->nrLDPC_coding_interface);

}

/* 中文说明：install_nr_schedule_handlers - 安装NR调度处理函数
 *
 * 函数作用：
 * - 为NR接口模块安装调度相关的回调函数
 * - 建立PHY层与MAC层之间的接口连接
 * - 设置配置请求和调度响应的处理函数
 *
 * 输入数据：
 * - if_inst：NR接口模块实例指针
 *
 * 数据处理：
 * - 设置PHY配置请求处理函数指针
 * - 设置调度响应处理函数指针
 *
 * 输出数据流向：
 * - 配置好的回调函数供FAPI接口调用
 * - 建立PHY-MAC接口通信机制
 *
 * 调用关系：
 * - 被接口初始化函数调用
 * - 设置的回调函数被FAPI层调用
 */
void install_nr_schedule_handlers(NR_IF_Module_t *if_inst)
{
  // 安装PHY配置请求处理函数，用于处理来自上层的配置请求
  if_inst->NR_PHY_config_req = nr_phy_config_request;
  // 安装调度响应处理函数，用于处理来自MAC层的调度指令
  if_inst->NR_Schedule_response = nr_schedule_response;
}

/* 中文说明：nr_phy_config_request_sim - NR PHY仿真配置请求函数
 *
 * 函数作用：
 * - 为仿真环境配置gNB的PHY层参数
 * - 设置小区ID、子载波间隔、资源块数量等基本参数
 * - 配置SSB（同步信号块）相关参数
 * - 初始化帧参数和载波频率
 *
 * 输入数据：
 * - gNB：待配置的gNB实例指针
 * - N_RB_DL：下行资源块数量
 * - N_RB_UL：上行资源块数量
 * - mu：子载波间隔参数（numerology）
 * - Nid_cell：物理小区ID
 * - position_in_burst：SSB在burst中的位置掩码
 *
 * 数据处理：
 * - 解析输入参数并填充NFAPI配置结构
 * - 计算SSB偏移和掩码
 * - 设置载波频率和带宽参数
 * - 初始化帧参数结构
 *
 * 输出数据流向：
 * - 配置好的gNB_config供物理层使用
 * - 初始化的frame_parms供信号处理使用
 *
 * 调用关系：
 * - 被仿真初始化函数调用
 * - 调用nr_init_frame_parms进行帧参数初始化
 */
void nr_phy_config_request_sim(PHY_VARS_gNB *gNB,
                               int N_RB_DL,
                               int N_RB_UL,
                               int mu,
                               int Nid_cell,
                               uint64_t position_in_burst)
{
  // 获取帧参数和配置结构的快捷指针
  NR_DL_FRAME_PARMS *fp                                   = &gNB->frame_parms;
  nfapi_nr_config_request_scf_t *gNB_config               = &gNB->gNB_config;

  // 重写新的NR参数

  // 反转position_in_burst的比特顺序（从MSB到LSB）
  uint64_t rev_burst=0;
  for (int i=0; i<64; i++)
    rev_burst |= (((position_in_burst>>(63-i))&0x01)<<i);

  // 配置基本小区参数
  gNB_config->cell_config.phy_cell_id.value             = Nid_cell;        // 物理小区ID
  gNB_config->ssb_config.scs_common.value               = mu;              // 公共子载波间隔
  gNB_config->ssb_table.ssb_subcarrier_offset.value     = 0;               // SSB子载波偏移
  gNB_config->ssb_table.ssb_offset_point_a.value        = (N_RB_DL-20)>>1; // SSB相对于点A的偏移

  // 设置SSB掩码（分为高32位和低32位）
  gNB_config->ssb_table.ssb_mask_list[1].ssb_mask.value = (rev_burst)&(0xFFFFFFFF);      // 低32位
  gNB_config->ssb_table.ssb_mask_list[0].ssb_mask.value = (rev_burst>>32)&(0xFFFFFFFF);  // 高32位

  // 配置帧结构参数
  gNB_config->cell_config.frame_duplex_type.value       = TDD;             // 时分双工
  gNB_config->ssb_table.ssb_period.value                = 1;               // SSB周期：10ms

  // 配置载波参数
  gNB_config->carrier_config.dl_grid_size[mu].value     = N_RB_DL;         // 下行网格大小
  gNB_config->carrier_config.ul_grid_size[mu].value     = N_RB_UL;         // 上行网格大小
  gNB_config->carrier_config.num_tx_ant.value           = fp->nb_antennas_tx; // 发送天线数
  gNB_config->carrier_config.num_rx_ant.value           = fp->nb_antennas_rx; // 接收天线数

  gNB_config->tdd_table.tdd_period.value = 0;
  //gNB_config->subframe_config.dl_cyclic_prefix_type.value = (fp->Ncp == NORMAL) ? NFAPI_CP_NORMAL : NFAPI_CP_EXTENDED;

  if (mu==0) {
    fp->dl_CarrierFreq = 2600000000;//from_nrarfcn(gNB_config->nfapi_config.rf_bands.rf_band[0],gNB_config->nfapi_config.nrarfcn.value);
    fp->ul_CarrierFreq = 2600000000;//fp->dl_CarrierFreq - (get_uldl_offset(gNB_config->nfapi_config.rf_bands.rf_band[0])*100000);
    fp->nr_band = 38;
    //  fp->threequarter_fs= 0;
  } else if (mu==1) {
    fp->dl_CarrierFreq = 3600000000;//from_nrarfcn(gNB_config->nfapi_config.rf_bands.rf_band[0],gNB_config->nfapi_config.nrarfcn.value);
    fp->ul_CarrierFreq = 3600000000;//fp->dl_CarrierFreq - (get_uldl_offset(gNB_config->nfapi_config.rf_bands.rf_band[0])*100000);
    fp->nr_band = 78;
    //  fp->threequarter_fs= 0;
  } else if (mu==3) {
    fp->dl_CarrierFreq = 27524520000;//from_nrarfcn(gNB_config->nfapi_config.rf_bands.rf_band[0],gNB_config->nfapi_config.nrarfcn.value);
    fp->ul_CarrierFreq = 27524520000;//fp->dl_CarrierFreq - (get_uldl_offset(gNB_config->nfapi_config.rf_bands.rf_band[0])*100000);
    fp->nr_band = 261;
    //  fp->threequarter_fs= 0;
  }

  fp->threequarter_fs = 0;
  frequency_range_t frequency_range = get_freq_range_from_band(fp->nr_band);
  int bw_index = get_supported_band_index(mu, frequency_range, N_RB_DL);
  gNB_config->carrier_config.dl_bandwidth.value = get_supported_bw_mhz(frequency_range, bw_index);

  nr_init_frame_parms(gNB_config, fp);

  fp->ofdm_offset_divisor = UINT_MAX;
  init_symbol_rotation(fp);
  init_timeshift_rotation(fp);

  gNB->configured = 1;
}

void nr_phy_config_request(NR_PHY_Config_t *phy_config)
{
  uint8_t Mod_id = phy_config->Mod_id;
  uint8_t short_sequence, num_sequences, rootSequenceIndex, fd_occasion;
  NR_DL_FRAME_PARMS *fp = &RC.gNB[Mod_id]->frame_parms;
  nfapi_nr_config_request_scf_t *gNB_config = &RC.gNB[Mod_id]->gNB_config;

  memcpy((void*)gNB_config,phy_config->cfg,sizeof(*phy_config->cfg));

  uint64_t dl_bw_khz = (12*gNB_config->carrier_config.dl_grid_size[gNB_config->ssb_config.scs_common.value].value)*(15<<gNB_config->ssb_config.scs_common.value);
  fp->dl_CarrierFreq = ((dl_bw_khz>>1) + gNB_config->carrier_config.dl_frequency.value)*1000 ;
  
  uint64_t ul_bw_khz = (12*gNB_config->carrier_config.ul_grid_size[gNB_config->ssb_config.scs_common.value].value)*(15<<gNB_config->ssb_config.scs_common.value);
  fp->ul_CarrierFreq = ((ul_bw_khz>>1) + gNB_config->carrier_config.uplink_frequency.value)*1000 ;

  int32_t dlul_offset = fp->ul_CarrierFreq - fp->dl_CarrierFreq;
  fp->nr_band = get_band(fp->dl_CarrierFreq, dlul_offset);

  LOG_I(PHY, "DL frequency %lu Hz, UL frequency %lu Hz: band %d, uldl offset %d Hz\n", fp->dl_CarrierFreq, fp->ul_CarrierFreq, fp->nr_band, dlul_offset);

  fp->threequarter_fs = get_softmodem_params()->threequarter_fs;
  LOG_D(PHY,"Configuring MIB for instance %d, : (Nid_cell %d,DL freq %llu, UL freq %llu)\n",
        Mod_id,
        gNB_config->cell_config.phy_cell_id.value,
        (unsigned long long)fp->dl_CarrierFreq,
        (unsigned long long)fp->ul_CarrierFreq);

  nr_init_frame_parms(gNB_config, fp);
  

  if (RC.gNB[Mod_id]->configured == 1) {
    LOG_E(PHY,"Already gNB already configured, do nothing\n");
    return;
  }

  fd_occasion = 0;
  nfapi_nr_prach_config_t *prach_config = &gNB_config->prach_config;
  short_sequence = prach_config->prach_sequence_length.value;
//  for(fd_occasion = 0; fd_occasion <= prach_config->num_prach_fd_occasions.value ; fd_occasion) { // TODO Need to handle for msg1-fdm > 1
  num_sequences = prach_config->num_prach_fd_occasions_list[fd_occasion].num_root_sequences.value;
  rootSequenceIndex = prach_config->num_prach_fd_occasions_list[fd_occasion].prach_root_sequence_index.value;

  compute_nr_prach_seq(short_sequence, num_sequences, rootSequenceIndex, RC.gNB[Mod_id]->X_u);
//  }
  RC.gNB[Mod_id]->configured     = 1;

  fp->ofdm_offset_divisor = RC.gNB[Mod_id]->ofdm_offset_divisor;
  init_symbol_rotation(fp);
  init_timeshift_rotation(fp);
}

void init_DLSCH_struct(PHY_VARS_gNB *gNB, processingData_L1tx_t *msg)
{
  NR_DL_FRAME_PARMS *fp = &gNB->frame_parms;
  nfapi_nr_config_request_scf_t *cfg = &gNB->gNB_config;
  uint16_t grid_size = cfg->carrier_config.dl_grid_size[fp->numerology_index].value;
  msg->num_pdsch_slot = 0;

  msg->dlsch = malloc16(gNB->max_nb_pdsch * sizeof(NR_gNB_DLSCH_t *));
  int num_cw = NR_MAX_NB_LAYERS > 4? 2:1;
  for (int i = 0; i < gNB->max_nb_pdsch; i++) {
    LOG_D(PHY, "Allocating Transport Channel Buffers for DLSCH %d/%d\n", i, gNB->max_nb_pdsch);
    msg->dlsch[i] = (NR_gNB_DLSCH_t *)malloc16(num_cw * sizeof(NR_gNB_DLSCH_t));
    for (int j = 0; j < num_cw; j++) {
      msg->dlsch[i][j] = new_gNB_dlsch(fp, grid_size);
    }
  }
}

void reset_DLSCH_struct(const PHY_VARS_gNB *gNB, processingData_L1tx_t *msg)
{
  const NR_DL_FRAME_PARMS *fp = &gNB->frame_parms;
  const nfapi_nr_config_request_scf_t *cfg = &gNB->gNB_config;
  const uint16_t grid_size = cfg->carrier_config.dl_grid_size[fp->numerology_index].value;
  int num_cw = NR_MAX_NB_LAYERS > 4? 2:1;
  for (int i = 0; i < gNB->max_nb_pdsch; i++) {
    for (int j = 0; j < num_cw; j++) {
      free_gNB_dlsch(&msg->dlsch[i][j], grid_size, fp);
    }
    free(msg->dlsch[i]);
  }
  free(msg->dlsch);
}

void init_nr_transport(PHY_VARS_gNB *gNB)
{

  NR_DL_FRAME_PARMS *fp = &gNB->frame_parms;
  const nfapi_nr_config_request_scf_t *cfg = &gNB->gNB_config;

  int nb_slots_per_period = cfg->cell_config.frame_duplex_type.value ?
                            fp->slots_per_frame / get_nb_periods_per_frame(cfg->tdd_table.tdd_period.value) :
                            fp->slots_per_frame;
  int nb_ul_slots_period = 0;
  if (cfg->cell_config.frame_duplex_type.value) {
    for(int i=0; i<nb_slots_per_period; i++) {
      for(int j=0; j<NR_NUMBER_OF_SYMBOLS_PER_SLOT; j++) {
        if(cfg->tdd_table.max_tdd_periodicity_list[i].max_num_of_symbol_per_slot_list[j].slot_config.value == 1) { // UL symbol
          nb_ul_slots_period++;
          break;
        }  
      }
    }
  }
  else
    nb_ul_slots_period = fp->slots_per_frame;

  int buffer_ul_slots; // the UL channels are scheduled sl_ahead before they are transmitted
  int slot_ahead = gNB->if_inst ? gNB->if_inst->sl_ahead : 6;
  if (slot_ahead > nb_slots_per_period)
    buffer_ul_slots = nb_ul_slots_period + (slot_ahead - nb_slots_per_period);
  else
    buffer_ul_slots = (nb_ul_slots_period < slot_ahead) ? nb_ul_slots_period : slot_ahead;

  gNB->max_nb_pucch = buffer_ul_slots ? MAX_MOBILES_PER_GNB * buffer_ul_slots : 1;
  gNB->max_nb_pusch = buffer_ul_slots ? MAX_MOBILES_PER_GNB * buffer_ul_slots : 1;
  gNB->max_nb_srs = buffer_ul_slots ? buffer_ul_slots << 1 : 1; // assuming at most 2 SRS per slot

  gNB->pucch = (NR_gNB_PUCCH_t *)malloc16(gNB->max_nb_pucch * sizeof(NR_gNB_PUCCH_t));
  for (int i = 0; i < gNB->max_nb_pucch; i++) {
    memset(&gNB->pucch[i], 0, sizeof(gNB->pucch[i]));
  }

  gNB->srs = (NR_gNB_SRS_t *)malloc16(gNB->max_nb_srs * sizeof(NR_gNB_SRS_t));
  for (int i = 0; i < gNB->max_nb_srs; i++)
    gNB->srs[i].active = 0;

  gNB->ulsch = (NR_gNB_ULSCH_t *)malloc16(gNB->max_nb_pusch * sizeof(NR_gNB_ULSCH_t));
  for (int i = 0; i < gNB->max_nb_pusch; i++) {
    LOG_D(PHY, "Allocating Transport Channel Buffers for ULSCH %d/%d\n", i, gNB->max_nb_pusch);
    gNB->ulsch[i] = new_gNB_ulsch(gNB->max_ldpc_iterations, fp->N_RB_UL);
  }

  gNB->rx_total_gain_dB=130;

  //fp->pucch_config_common.deltaPUCCH_Shift = 1;
}

void reset_nr_transport(PHY_VARS_gNB *gNB)
{
  const NR_DL_FRAME_PARMS *fp = &gNB->frame_parms;

  free(gNB->pucch);
  free(gNB->srs);

  for (int i = 0; i < gNB->max_nb_pusch; i++)
    free_gNB_ulsch(&gNB->ulsch[i], fp->N_RB_UL);
  free(gNB->ulsch);
}
