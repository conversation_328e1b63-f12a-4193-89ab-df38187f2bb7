/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/* 中文说明：本文件是NR参数配置和帧参数初始化的核心文件
 * 主要功能：
 * 1. 定义NR系统的基本参数（子载波间隔、时隙配置等）
 * 2. 初始化NR帧参数结构（NR_DL_FRAME_PARMS）
 * 3. 配置SSB（同步信号块）相关参数
 * 4. 处理不同numerology（子载波间隔配置）的参数设置
 *
 * 数据流向：
 * 输入数据来源：
 * - NFAPI配置请求（nfapi_nr_config_request_scf_t）
 * - 系统配置参数（带宽、频率、天线配置等）
 * - 用户设备配置参数
 *
 * 输出数据流向：
 * - 初始化完成的NR_DL_FRAME_PARMS结构体
 * - 配置好的帧参数供物理层各模块使用
 * - SSB参数供同步和广播信道使用
 *
 * 调用关系：
 * - 被gNB和UE初始化函数调用
 * - 为物理层信号处理提供基础参数
 * - 与其他初始化模块协同工作
 */

#include "nr_phy_init.h"                    // NR PHY初始化头文件
#include "common/utils/nr/nr_common.h"      // NR通用工具函数
#include "common/utils/LOG/log.h"           // 日志记录工具
#include "executables/softmodem-common.h"   // 软调制解调器通用定义
#include "PHY/MODULATION/nr_modulation.h"   // NR调制解调定义

/// 子载波间隔（Hz），按numerology索引排列
static const uint32_t nr_subcarrier_spacing[MAX_NUM_SUBCARRIER_SPACING] = {15e3, 30e3, 60e3, 120e3, 240e3};
/// 每子帧的时隙数，按numerology索引排列
static const uint16_t nr_slots_per_subframe[MAX_NUM_SUBCARRIER_SPACING] = {1, 2, 4, 8, 16};

/* 中文说明：NR SSB配置表 - 基于3GPP TS 38.101表5.4.3.3-1
 *
 * 表结构：{频段号, 子载波间隔(kHz), SSB类型}
 *
 * SSB类型说明：
 * - nr_ssb_type_A: 适用于FR1频段，15kHz子载波间隔
 * - nr_ssb_type_B: 适用于FR1频段，30kHz子载波间隔，特定频段
 * - nr_ssb_type_C: 适用于FR1频段，30kHz子载波间隔，高频段
 * - nr_ssb_type_D: 适用于FR2频段，120kHz子载波间隔
 * - nr_ssb_type_E: 适用于FR2频段，240kHz子载波间隔
 *
 * 数据用途：
 * - 根据频段和子载波间隔确定SSB类型
 * - 配置SSB的时域位置和符号分布
 * - 用于初始化同步信号块参数
 */
// 表5.4.3.3-1 来自3GPP TS 38.101
static const int nr_ssb_table[][3] = {
    {1, 15, nr_ssb_type_A},    // 频段1，15kHz，类型A
    {2, 15, nr_ssb_type_A},    // 频段2，15kHz，类型A
    {3, 15, nr_ssb_type_A},    // 频段3，15kHz，类型A
    {5, 15, nr_ssb_type_A},    // 频段5，15kHz，类型A
    {5, 30, nr_ssb_type_B},    // 频段5，30kHz，类型B
    {7, 15, nr_ssb_type_A},    // 频段7，15kHz，类型A
    {8, 15, nr_ssb_type_A},    // 频段8，15kHz，类型A
    {12, 15, nr_ssb_type_A},   // 频段12，15kHz，类型A
    {13, 15, nr_ssb_type_A},   // 频段13，15kHz，类型A
    {14, 15, nr_ssb_type_A},   // 频段14，15kHz，类型A
    {18, 15, nr_ssb_type_A},   // 频段18，15kHz，类型A
    {20, 15, nr_ssb_type_A},   // 频段20，15kHz，类型A
    {24, 15, nr_ssb_type_A},   // 频段24，15kHz，类型A
    {24, 30, nr_ssb_type_B},   // 频段24，30kHz，类型B
    {25, 15, nr_ssb_type_A},   // 频段25，15kHz，类型A
    {26, 15, nr_ssb_type_A},   // 频段26，15kHz，类型A
    {28, 15, nr_ssb_type_A},   // 频段28，15kHz，类型A
    {29, 15, nr_ssb_type_A},   // 频段29，15kHz，类型A
    {30, 15, nr_ssb_type_A},   // 频段30，15kHz，类型A
    {34, 15, nr_ssb_type_A},   // 频段34，15kHz，类型A
    {34, 30, nr_ssb_type_C},   // 频段34，30kHz，类型C
    {38, 15, nr_ssb_type_A},   // 频段38，15kHz，类型A
    {38, 30, nr_ssb_type_C},   // 频段38，30kHz，类型C
    {39, 15, nr_ssb_type_A},   // 频段39，15kHz，类型A
    {39, 30, nr_ssb_type_C},   // 频段39，30kHz，类型C
    {40, 30, nr_ssb_type_C},   // 频段40，30kHz，类型C
    {41, 15, nr_ssb_type_A},   // 频段41，15kHz，类型A
    {41, 30, nr_ssb_type_C},   // 频段41，30kHz，类型C
    {46, 30, nr_ssb_type_C},   // 频段46，30kHz，类型C
    {48, 30, nr_ssb_type_C},   // 频段48，30kHz，类型C
    {50, 30, nr_ssb_type_C},   // 频段50，30kHz，类型C
    {51, 15, nr_ssb_type_A},   // 频段51，15kHz，类型A
    {53, 15, nr_ssb_type_A},   // 频段53，15kHz，类型A
    {53, 30, nr_ssb_type_C},   // 频段53，30kHz，类型C
    {65, 15, nr_ssb_type_A},   // 频段65，15kHz，类型A
    {66, 15, nr_ssb_type_A},   // 频段66，15kHz，类型A
    {66, 30, nr_ssb_type_B},   // 频段66，30kHz，类型B
    {67, 15, nr_ssb_type_A},   // 频段67，15kHz，类型A
    {70, 15, nr_ssb_type_A},   // 频段70，15kHz，类型A
    {71, 15, nr_ssb_type_A},   // 频段71，15kHz，类型A
    {74, 15, nr_ssb_type_A},   // 频段74，15kHz，类型A
    {75, 15, nr_ssb_type_A},   // 频段75，15kHz，类型A
    {76, 15, nr_ssb_type_A},   // 频段76，15kHz，类型A
    {77, 30, nr_ssb_type_C},   // 频段77，30kHz，类型C
    {78, 30, nr_ssb_type_C},   // 频段78，30kHz，类型C
    {79, 30, nr_ssb_type_C},   // 频段79，30kHz，类型C
    {85, 15, nr_ssb_type_A},   // 频段85，15kHz，类型A
    {90, 15, nr_ssb_type_A},   // 频段90，15kHz，类型A
    {90, 30, nr_ssb_type_C},   // 频段90，30kHz，类型C
    {91, 15, nr_ssb_type_A},   // 频段91，15kHz，类型A
    {92, 15, nr_ssb_type_A},   // 频段92，15kHz，类型A
    {93, 15, nr_ssb_type_A},   // 频段93，15kHz，类型A
    {94, 15, nr_ssb_type_A},   // 频段94，15kHz，类型A
    {96, 30, nr_ssb_type_C},   // 频段96，30kHz，类型C
    {100, 15, nr_ssb_type_A},  // 频段100，15kHz，类型A
    {101, 15, nr_ssb_type_A},  // 频段101，15kHz，类型A
    {101, 30, nr_ssb_type_C},  // 频段101，30kHz，类型C
    {102, 30, nr_ssb_type_C},  // 频段102，30kHz，类型C
    {104, 30, nr_ssb_type_C},  // 频段104，30kHz，类型C
    {254, 15, nr_ssb_type_A},  // 频段254，15kHz，类型A（测试频段）
    {254, 30, nr_ssb_type_C},  // 频段254，30kHz，类型C（测试频段）
    {255, 15, nr_ssb_type_A},  // 频段255，15kHz，类型A（测试频段）
    {255, 30, nr_ssb_type_B},  // 频段255，30kHz，类型B（测试频段）
    {256, 15, nr_ssb_type_A}}; // 频段256，15kHz，类型A（测试频段）

/* 中文说明：set_Lmax - 设置SSB的最大候选位置数
 *
 * 函数作用：
 * - 根据载波频率和帧类型设置SSB的最大候选位置数Lmax
 * - 遵循3GPP TS 38.213第4.1节的定义
 * - 用于确定SSB在时域中的可能位置数量
 *
 * 输入数据：
 * - fp：帧参数结构指针
 * - fp->dl_CarrierFreq：下行载波频率
 * - fp->frame_type：帧类型（FDD/TDD）
 * - fp->ssb_type：SSB类型
 *
 * 数据处理：
 * - FR1频段（<6GHz）：根据频率和帧类型设置Lmax为4或8
 * - FR2频段（≥6GHz）：设置Lmax为64
 * - TDD且SSB类型为2时有特殊处理
 *
 * 输出数据流向：
 * - 设置fp->Lmax值供SSB处理使用
 * - 影响SSB检测和测量的候选位置数量
 *
 * 调用关系：
 * - 被帧参数初始化函数调用
 * - 用于SSB相关的物理层处理
 */
void set_Lmax(NR_DL_FRAME_PARMS *fp) {
  // 根据3GPP TS 38.213第4.1节定义Lmax
  if (fp->dl_CarrierFreq < 6e9) {  // FR1频段（低于6GHz）
    if(fp->frame_type && (fp->ssb_type==2))  // TDD且SSB类型为2
      fp->Lmax = (fp->dl_CarrierFreq < 2.4e9)? 4 : 8;  // 低于2.4GHz为4，否则为8
    else
      fp->Lmax = (fp->dl_CarrierFreq < 3e9)? 4 : 8;    // 低于3GHz为4，否则为8
  } else {  // FR2频段（6GHz及以上）
    fp->Lmax = 64;  // FR2频段固定为64个候选位置
  }
}

/* 中文说明：nr_get_ssb_start_symbol - 获取SSB起始符号位置
 *
 * 函数作用：
 * - 根据numerology和SSB索引计算SSB的起始OFDM符号位置
 * - 实现3GPP TS 38.213中定义的SSB时域位置映射
 * - 支持不同SSB类型（A、B、C、D、E）的符号计算
 *
 * 输入数据：
 * - fp：帧参数结构指针（包含numerology和SSB类型）
 * - i_ssb：SSB索引（在候选位置中的索引）
 *
 * 数据处理：
 * - 根据numerology选择相应的计算case
 * - 使用预定义的符号偏移数组计算起始位置
 * - 考虑半帧内的重复模式
 *
 * 输出数据流向：
 * - 返回SSB起始符号位置
 * - 用于SSB发送和接收的时域定位
 *
 * 调用关系：
 * - 被SSB处理函数调用
 * - 用于同步信号的时域映射
 */
int nr_get_ssb_start_symbol(const NR_DL_FRAME_PARMS *fp, uint8_t i_ssb)
{
  int mu = fp->numerology_index;  // 获取numerology索引
  int symbol = 0;                 // 起始符号位置
  uint8_t n, n_temp;             // 临时变量
  nr_ssb_type_e type = fp->ssb_type;  // SSB类型

  // 不同case的符号偏移数组
  int case_AC[2] = {2,8};                              // Case A和C的符号偏移
  int case_BD[4] = {4,8,16,20};                        // Case B和D的符号偏移
  int case_E[8] = {8, 12, 16, 20, 32, 36, 40, 44};    // Case E的符号偏移

  switch(mu) {
    case NR_MU_0: // Case A (15kHz子载波间隔)
      n = i_ssb >> 1;                           // 半帧索引
      symbol = case_AC[i_ssb % 2] + 14*n;       // 符号位置 = 基础偏移 + 14*半帧索引
      break;

    case NR_MU_1: // 30kHz子载波间隔
      if (type == 1){ // Case B
        n = i_ssb >> 2;                         // 每4个SSB为一组
        symbol = case_BD[i_ssb % 4] + 28*n;     // 符号位置 = 基础偏移 + 28*组索引
       }
       if (type == 2){ // Case C
         n = i_ssb >> 1;                        // 半帧索引
         symbol = case_AC[i_ssb % 2] + 14*n;    // 符号位置 = 基础偏移 + 14*半帧索引
       }
       break;

     case NR_MU_3: // Case D (120kHz子载波间隔)
       n_temp = i_ssb >> 2;                     // 临时组索引
       n = n_temp + (n_temp >> 2);              // 考虑间隔的实际组索引
       symbol = case_BD[i_ssb % 4] + 28*n;      // 符号位置计算
       break;

     case NR_MU_4:  // Case E (240kHz子载波间隔)
       n_temp = i_ssb >> 3;                     // 每8个SSB为一组
       n = n_temp + (n_temp >> 2);              // 考虑间隔的实际组索引
       symbol = case_E[i_ssb % 8] + 56*n;       // 符号位置 = 基础偏移 + 56*组索引
       break;

     default:
       AssertFatal(0==1, "Invalid numerology index %d for the synchronization block\n", mu);
  }

  return symbol;  // 返回计算得到的起始符号位置
}

/* 中文说明：set_scs_parameters - 设置子载波间隔相关参数
 *
 * 函数作用：
 * - 根据numerology设置子载波间隔和时隙配置
 * - 配置OFDM符号大小和CP长度
 * - 设置载波偏移和前缀样本数
 * - 验证频段与子载波间隔的兼容性
 *
 * 输入数据：
 * - fp：帧参数结构指针
 * - mu：numerology索引（0-4，对应15/30/60/120/240kHz）
 * - N_RB_DL：下行资源块数量
 *
 * 数据处理：
 * - 根据mu设置子载波间隔和每子帧时隙数
 * - 查表确定SSB类型
 * - 计算OFDM符号大小（必须大于等于总子载波数）
 * - 计算载波偏移和CP样本数
 *
 * 输出数据流向：
 * - 配置好的帧参数供OFDM调制解调使用
 * - CP参数供循环前缀处理使用
 * - 载波偏移供频域处理使用
 *
 * 调用关系：
 * - 被帧参数初始化函数调用
 * - 为OFDM信号处理提供基础参数
 */
void set_scs_parameters (NR_DL_FRAME_PARMS *fp, int mu, int N_RB_DL)
{
  int idx = 0;  // SSB表索引

  switch(mu) {
    case NR_MU_0: // 15kHz子载波间隔
      fp->subcarrier_spacing = nr_subcarrier_spacing[NR_MU_0];  // 15000 Hz
      fp->slots_per_subframe = nr_slots_per_subframe[NR_MU_0];  // 1个时隙/子帧
      fp->ssb_type = nr_ssb_type_A;  // 默认SSB类型A

      // 在SSB表中查找匹配的频段
      while (nr_ssb_table[idx][0] != fp->nr_band)
        idx++;
      // 验证15kHz子载波间隔适用于该频段
      AssertFatal(nr_ssb_table[idx][1] == 15,"SCS %d not applicable to band %d\n",
                  fp->subcarrier_spacing,fp->nr_band);
      break;

    case NR_MU_1: // 30kHz子载波间隔
      fp->subcarrier_spacing = nr_subcarrier_spacing[NR_MU_1];  // 30000 Hz
      fp->slots_per_subframe = nr_slots_per_subframe[NR_MU_1];  // 2个时隙/子帧

      // 查找匹配频段和30kHz子载波间隔的表项
      while(nr_ssb_table[idx][0] != fp->nr_band ||
            nr_ssb_table[idx][1] != 30) {
        AssertFatal(nr_ssb_table[idx][0] <= fp->nr_band,
                    "SCS %d not applicable to band %d\n",
                    fp->subcarrier_spacing,
                    fp->nr_band);
        idx++;
      }
      fp->ssb_type = nr_ssb_table[idx][2];  // 从表中获取SSB类型
      break;

    case NR_MU_2: // 60kHz子载波间隔
      fp->subcarrier_spacing = nr_subcarrier_spacing[NR_MU_2];  // 60000 Hz
      fp->slots_per_subframe = nr_slots_per_subframe[NR_MU_2];  // 4个时隙/子帧
      break;

    case NR_MU_3: // 120kHz子载波间隔
      fp->subcarrier_spacing = nr_subcarrier_spacing[NR_MU_3];  // 120000 Hz
      fp->slots_per_subframe = nr_slots_per_subframe[NR_MU_3];  // 8个时隙/子帧
      fp->ssb_type = nr_ssb_type_D;  // FR2频段SSB类型D
      break;

    case NR_MU_4: // 240kHz子载波间隔
      fp->subcarrier_spacing = nr_subcarrier_spacing[NR_MU_4];  // 240000 Hz
      fp->slots_per_subframe = nr_slots_per_subframe[NR_MU_4];  // 16个时隙/子帧
      fp->ssb_type = nr_ssb_type_E;  // FR2频段SSB类型E
      break;

    default:
      AssertFatal(1==0,"Invalid numerology index %d", mu);
  }

  // 设置OFDM符号大小
  if(fp->threequarter_fs)  // 3/4采样率模式
    fp->ofdm_symbol_size = 3 * 128;  // 384点FFT
  else
    fp->ofdm_symbol_size = 4 * 128;  // 512点FFT

  // 确保OFDM符号大小足够容纳所有子载波（每RB 12个子载波）
  while(fp->ofdm_symbol_size < N_RB_DL * 12)
    fp->ofdm_symbol_size <<= 1;  // 左移1位（乘以2）

  // 计算第一个载波的偏移量（中心频率偏移）
  fp->first_carrier_offset = fp->ofdm_symbol_size - (N_RB_DL * 12 / 2);

  // 计算循环前缀样本数
  fp->nb_prefix_samples    = fp->ofdm_symbol_size / 128 * 9;           // 普通CP长度
  fp->nb_prefix_samples0   = fp->ofdm_symbol_size / 128 * (9 + (1 << mu)); // 第一个符号的扩展CP长度

  LOG_I(PHY,
        "Init: N_RB_DL %d, first_carrier_offset %d, nb_prefix_samples %d,nb_prefix_samples0 %d, ofdm_symbol_size %d\n",
        N_RB_DL,
        fp->first_carrier_offset,
        fp->nb_prefix_samples,
        fp->nb_prefix_samples0,
        fp->ofdm_symbol_size);
}

void sl_set_scs_parameters (NR_DL_FRAME_PARMS *fp, int mu, int N_RB_SL)
{

  AssertFatal(mu >= NR_MU_0 && mu <= NR_MU_4,"Invalid numerology index %d", mu);

  fp->subcarrier_spacing = nr_subcarrier_spacing[mu];
  fp->slots_per_subframe = nr_slots_per_subframe[mu];

  if(fp->threequarter_fs)
    fp->ofdm_symbol_size = 3 * 128;
  else
    fp->ofdm_symbol_size = 4 * 128;

  while(fp->ofdm_symbol_size < N_RB_SL * 12)
    fp->ofdm_symbol_size <<= 1;

  fp->first_carrier_offset = fp->ofdm_symbol_size - (N_RB_SL * 12 / 2);
  fp->nb_prefix_samples    = fp->ofdm_symbol_size / 128 * 9;
  fp->nb_prefix_samples0   = fp->ofdm_symbol_size / 128 * (9 + (1 << mu));
  LOG_I(PHY,
        "Init: N_RB_SL %d, first_carrier_offset %d, nb_prefix_samples %d,nb_prefix_samples0 %d, ofdm_symbol_size %d\n",
        N_RB_SL,
        fp->first_carrier_offset,
        fp->nb_prefix_samples,
        fp->nb_prefix_samples0,
        fp->ofdm_symbol_size);
}

uint32_t get_samples_per_slot(int slot, const NR_DL_FRAME_PARMS *fp)
{
  uint32_t samp_count;

  if(fp->numerology_index == 0)
    samp_count = fp->samples_per_subframe;
  else
    samp_count = (slot % (fp->slots_per_subframe / 2)) ? fp->samples_per_slotN0 : fp->samples_per_slot0;

  return samp_count;
}

uint32_t get_slot_from_timestamp(openair0_timestamp timestamp_rx, const NR_DL_FRAME_PARMS *fp)
{
   uint32_t slot_idx = 0;
   int samples_till_the_slot = fp->get_samples_per_slot(slot_idx,fp)-1;
   timestamp_rx = timestamp_rx%fp->samples_per_frame;

    while (timestamp_rx > samples_till_the_slot) {
        slot_idx++;
        samples_till_the_slot += fp->get_samples_per_slot(slot_idx,fp);
     }
   return slot_idx; 
}

uint32_t get_samples_slot_timestamp(int slot, const NR_DL_FRAME_PARMS *fp, unsigned int sl_ahead)
{
  uint32_t samp_count = 0;

  if(!sl_ahead) {
    for(unsigned int idx_slot = 0; idx_slot < slot; idx_slot++)
      samp_count += fp->get_samples_per_slot(idx_slot, fp);
  } else {
    for (unsigned int idx_slot = slot; idx_slot < slot + sl_ahead; idx_slot++)
      samp_count += fp->get_samples_per_slot(idx_slot, fp);
  }
  return samp_count;
}

void nr_init_frame_parms(nfapi_nr_config_request_scf_t* cfg, NR_DL_FRAME_PARMS *fp)
{

  AssertFatal (cfg, "Null pointer to cfg!\n");

  fp->frame_type = cfg->cell_config.frame_duplex_type.value;
  fp->L_ssb = (((uint64_t) cfg->ssb_table.ssb_mask_list[0].ssb_mask.value) << 32) | cfg->ssb_table.ssb_mask_list[1].ssb_mask.value;
  fp->N_RB_DL = cfg->carrier_config.dl_grid_size[cfg->ssb_config.scs_common.value].value;
  fp->N_RB_UL = cfg->carrier_config.ul_grid_size[cfg->ssb_config.scs_common.value].value;

  int Ncp = NFAPI_CP_NORMAL;
  int mu = cfg->ssb_config.scs_common.value;

  LOG_I(PHY,"Initializing frame parms for mu %d, N_RB %d, Ncp %d\n", mu, fp->N_RB_DL, Ncp);

  if (Ncp == NFAPI_CP_EXTENDED)
    AssertFatal(mu == NR_MU_2,"Invalid cyclic prefix %d for numerology index %d\n", Ncp, mu);

  fp->half_frame_bit = 0;  // half frame bit initialized to 0 here
  fp->numerology_index = mu;

  set_scs_parameters(fp, mu, fp->N_RB_DL);

  fp->slots_per_frame = 10* fp->slots_per_subframe;

  fp->nb_antennas_rx = cfg->carrier_config.num_rx_ant.value;      // It denotes the number of rx antennas at gNB
  fp->nb_antennas_tx = cfg->carrier_config.num_tx_ant.value;      // It corresponds to pdsch_AntennaPorts (logical antenna ports)

  fp->symbols_per_slot = ((Ncp == NORMAL)? 14 : 12); // to redefine for different slot formats
  fp->samples_per_subframe_wCP = fp->ofdm_symbol_size * fp->symbols_per_slot * fp->slots_per_subframe;
  fp->samples_per_frame_wCP = 10 * fp->samples_per_subframe_wCP;
  fp->samples_per_slot_wCP = fp->symbols_per_slot*fp->ofdm_symbol_size; 
  fp->samples_per_slotN0 = (fp->nb_prefix_samples + fp->ofdm_symbol_size) * fp->symbols_per_slot;
  fp->samples_per_slot0 = fp->nb_prefix_samples0 + ((fp->symbols_per_slot-1)*fp->nb_prefix_samples) + (fp->symbols_per_slot*fp->ofdm_symbol_size); 
  fp->samples_per_subframe = (fp->nb_prefix_samples0 + fp->ofdm_symbol_size) * 2 + 
                             (fp->nb_prefix_samples + fp->ofdm_symbol_size) * (fp->symbols_per_slot * fp->slots_per_subframe - 2); 
  fp->get_samples_per_slot = &get_samples_per_slot;
  fp->get_samples_slot_timestamp = &get_samples_slot_timestamp;
  fp->get_slot_from_timestamp = &get_slot_from_timestamp;
  fp->samples_per_frame = 10 * fp->samples_per_subframe;
  fp->freq_range = get_freq_range_from_freq(fp->dl_CarrierFreq);

  fp->Ncp = Ncp;

  set_Lmax(fp);

  fp->N_ssb = 0;
  int num_tx_ant = cfg->carrier_config.num_tx_ant.value;

  for (int p=0; p<num_tx_ant; p++)
    fp->N_ssb += ((fp->L_ssb >> (63-p)) & 0x01);

  fp->print_ue_help_cmdline_log = true;
}

int nr_init_frame_parms_ue(NR_DL_FRAME_PARMS *fp,
                           fapi_nr_config_request_t* config,
                           uint16_t nr_band)
{

  uint8_t nb_ant_ports_gNB  = 1;
  uint8_t tdd_cfg           = 3;
  uint8_t Nid_cell          = 0;
  int     Ncp               = NORMAL;

  if(fp->nb_antennas_rx == 0)
    fp->nb_antennas_rx = 1;
  if(fp->nb_antennas_tx == 0)
    fp->nb_antennas_tx = 1;

  // default values until overwritten by RRCConnectionReconfiguration
  fp->nb_antenna_ports_gNB = nb_ant_ports_gNB;
  fp->tdd_config           = tdd_cfg;
  fp->Nid_cell             = Nid_cell;
  fp->nr_band              = nr_band;

  LOG_I(PHY, "Initializing frame parms: set nb_antenna_ports_gNB %d, tdd_config, %d, Nid_cell %d\n", fp->nb_antenna_ports_gNB, fp->tdd_config, fp->Nid_cell);

  uint64_t dl_bw_khz = (12*config->carrier_config.dl_grid_size[config->ssb_config.scs_common])*(15<<config->ssb_config.scs_common);
  fp->dl_CarrierFreq = ((dl_bw_khz>>1) + config->carrier_config.dl_frequency)*1000 ;

  LOG_D(PHY,"dl_bw_kHz %lu\n",dl_bw_khz);
  LOG_D(PHY,"dl_CarrierFreq %lu\n",fp->dl_CarrierFreq);

  uint64_t ul_bw_khz = (12*config->carrier_config.ul_grid_size[config->ssb_config.scs_common])*(15<<config->ssb_config.scs_common);
  fp->ul_CarrierFreq = ((ul_bw_khz>>1) + config->carrier_config.uplink_frequency)*1000 ;

  fp->numerology_index = config->ssb_config.scs_common;
  fp->N_RB_UL = config->carrier_config.ul_grid_size[fp->numerology_index];
  fp->N_RB_DL = config->carrier_config.dl_grid_size[fp->numerology_index];
  fp->N_RB_SL = config->carrier_config.sl_grid_size[fp->numerology_index];

  fp->frame_type = get_frame_type(fp->nr_band, fp->numerology_index);
  int32_t uplink_frequency_offset = get_delta_duplex(fp->nr_band, fp->numerology_index);
  uplink_frequency_offset *= 1000;

  LOG_I(PHY, "Initializing frame parms: DL frequency %lu Hz, UL frequency %lu Hz: band %d, uldl offset %d Hz\n", fp->dl_CarrierFreq, fp->ul_CarrierFreq, fp->nr_band, uplink_frequency_offset);

  AssertFatal(fp->frame_type==config->cell_config.frame_duplex_type, "Invalid duplex type (frame_type %d,cell_config.frame_duplex_type %d) in config request file for band %d\n", fp->frame_type,config->cell_config.frame_duplex_type,fp->nr_band);

  AssertFatal(fp->ul_CarrierFreq == (fp->dl_CarrierFreq + uplink_frequency_offset), "Disagreement in uplink frequency for band %d: ul_CarrierFreq = %lu Hz vs expected %lu Hz\n", fp->nr_band, fp->ul_CarrierFreq, fp->dl_CarrierFreq + uplink_frequency_offset);

  LOG_I(PHY,"Initializing frame parms for mu %d, N_RB %d, Ncp %d\n",fp->numerology_index, fp->N_RB_DL, Ncp);


  if (Ncp == NFAPI_CP_EXTENDED)
    AssertFatal(fp->numerology_index == NR_MU_2,"Invalid cyclic prefix %d for numerology index %d\n", Ncp, fp->numerology_index);

  fp->Ncp = Ncp;
  int N_RB = fp->N_RB_DL;
  set_scs_parameters(fp, fp->numerology_index, N_RB);

  fp->slots_per_frame = 10* fp->slots_per_subframe;
  fp->symbols_per_slot = ((Ncp == NORMAL)? 14 : 12); // to redefine for different slot formats
  fp->samples_per_subframe_wCP = fp->ofdm_symbol_size * fp->symbols_per_slot * fp->slots_per_subframe;
  fp->samples_per_frame_wCP = 10 * fp->samples_per_subframe_wCP;
  fp->samples_per_slot_wCP = fp->symbols_per_slot*fp->ofdm_symbol_size; 
  fp->samples_per_slotN0 = (fp->nb_prefix_samples + fp->ofdm_symbol_size) * fp->symbols_per_slot;
  fp->samples_per_slot0 = fp->nb_prefix_samples0 + ((fp->symbols_per_slot-1)*fp->nb_prefix_samples) + (fp->symbols_per_slot*fp->ofdm_symbol_size); 
  fp->samples_per_subframe = (fp->nb_prefix_samples0 + fp->ofdm_symbol_size) * 2 + 
                             (fp->nb_prefix_samples + fp->ofdm_symbol_size) * (fp->symbols_per_slot * fp->slots_per_subframe - 2); 
  fp->get_samples_per_slot = &get_samples_per_slot;
  fp->get_samples_slot_timestamp = &get_samples_slot_timestamp;
  fp->samples_per_frame = 10 * fp->samples_per_subframe;
  fp->freq_range = get_freq_range_from_freq(fp->dl_CarrierFreq);

  uint8_t sco = 0;
  if (((fp->freq_range == FR1) && (config->ssb_table.ssb_subcarrier_offset < 24)) ||
      ((fp->freq_range == FR2) && (config->ssb_table.ssb_subcarrier_offset < 12))) {
    if (fp->freq_range == FR1)
      sco = config->ssb_table.ssb_subcarrier_offset>>config->ssb_config.scs_common;
    else
      sco = config->ssb_table.ssb_subcarrier_offset;
  }

  fp->ssb_start_subcarrier = (12 * config->ssb_table.ssb_offset_point_a + sco);
  set_Lmax(fp);

  fp->L_ssb = (((uint64_t) config->ssb_table.ssb_mask_list[0].ssb_mask)<<32) | config->ssb_table.ssb_mask_list[1].ssb_mask;
  
  fp->N_ssb = 0;
  for (int p=0; p<fp->Lmax; p++)
    fp->N_ssb += ((fp->L_ssb >> (63-p)) & 0x01);

  return 0;
}

void nr_init_frame_parms_ue_sa(NR_DL_FRAME_PARMS *frame_parms, uint64_t downlink_frequency, int32_t delta_duplex, uint8_t mu, uint16_t nr_band) {

  LOG_I(PHY,"SA init parameters. DL freq %lu UL offset %d SSB numerology %d N_RB_DL %d\n",
        downlink_frequency,
        delta_duplex,
        mu,
        frame_parms->N_RB_DL);

  frame_parms->numerology_index = mu;
  frame_parms->dl_CarrierFreq = downlink_frequency;
  frame_parms->ul_CarrierFreq = downlink_frequency + delta_duplex;
  if (get_softmodem_params()->sl_mode == 0) {
    frame_parms->freq_range = get_freq_range_from_freq(frame_parms->dl_CarrierFreq);
  }
  frame_parms->N_RB_UL = frame_parms->N_RB_DL;

  frame_parms->nr_band = nr_band;
  frame_parms->frame_type = get_frame_type(frame_parms->nr_band, frame_parms->numerology_index);

  frame_parms->Ncp = NORMAL;
  set_scs_parameters(frame_parms, frame_parms->numerology_index, frame_parms->N_RB_DL);
  set_Lmax(frame_parms);

  frame_parms->slots_per_frame = 10* frame_parms->slots_per_subframe;
  frame_parms->symbols_per_slot = ((frame_parms->Ncp == NORMAL)? 14 : 12); // to redefine for different slot formats
  frame_parms->samples_per_subframe_wCP = frame_parms->ofdm_symbol_size * frame_parms->symbols_per_slot * frame_parms->slots_per_subframe;
  frame_parms->samples_per_frame_wCP = 10 * frame_parms->samples_per_subframe_wCP;
  frame_parms->samples_per_slot_wCP = frame_parms->symbols_per_slot*frame_parms->ofdm_symbol_size;
  frame_parms->samples_per_slotN0 = (frame_parms->nb_prefix_samples + frame_parms->ofdm_symbol_size) * frame_parms->symbols_per_slot;
  frame_parms->samples_per_slot0 = frame_parms->nb_prefix_samples0 + ((frame_parms->symbols_per_slot-1)*frame_parms->nb_prefix_samples) + (frame_parms->symbols_per_slot*frame_parms->ofdm_symbol_size);
  frame_parms->samples_per_subframe = (frame_parms->nb_prefix_samples0 + frame_parms->ofdm_symbol_size) * 2 +
                             (frame_parms->nb_prefix_samples + frame_parms->ofdm_symbol_size) * (frame_parms->symbols_per_slot * frame_parms->slots_per_subframe - 2);
  frame_parms->get_samples_per_slot = &get_samples_per_slot;
  frame_parms->get_samples_slot_timestamp = &get_samples_slot_timestamp;
  frame_parms->samples_per_frame = 10 * frame_parms->samples_per_subframe;

  LOG_W(PHY, "samples_per_subframe %d/per second %d, wCP %d\n", frame_parms->samples_per_subframe, 1000*frame_parms->samples_per_subframe, frame_parms->samples_per_subframe_wCP);

}

void nr_dump_frame_parms(NR_DL_FRAME_PARMS *fp)
{
  LOG_I(PHY,"fp->scs=%d\n",fp->subcarrier_spacing);
  LOG_I(PHY,"fp->ofdm_symbol_size=%d\n",fp->ofdm_symbol_size);
  LOG_I(PHY,"fp->nb_prefix_samples0=%d\n",fp->nb_prefix_samples0);
  LOG_I(PHY,"fp->nb_prefix_samples=%d\n",fp->nb_prefix_samples);
  LOG_I(PHY,"fp->slots_per_subframe=%d\n",fp->slots_per_subframe);
  LOG_I(PHY,"fp->samples_per_subframe_wCP=%d\n",fp->samples_per_subframe_wCP);
  LOG_I(PHY,"fp->samples_per_frame_wCP=%d\n",fp->samples_per_frame_wCP);
  LOG_I(PHY,"fp->samples_per_subframe=%d\n",fp->samples_per_subframe);
  LOG_I(PHY,"fp->samples_per_frame=%d\n",fp->samples_per_frame);
  LOG_I(PHY,"fp->dl_CarrierFreq=%lu\n",fp->dl_CarrierFreq);
  LOG_I(PHY,"fp->ul_CarrierFreq=%lu\n",fp->ul_CarrierFreq);
  LOG_I(PHY, "fp->Nid_cell=%d\n", fp->Nid_cell);
  LOG_I(PHY, "fp->first_carrier_offset=%d\n", fp->first_carrier_offset);
  LOG_I(PHY, "fp->ssb_start_subcarrier=%d\n", fp->ssb_start_subcarrier);
  LOG_I(PHY, "fp->Ncp=%d\n", fp->Ncp);
  LOG_I(PHY, "fp->N_RB_DL=%d\n", fp->N_RB_DL);
  LOG_I(PHY, "fp->numerology_index=%d\n", fp->numerology_index);
  LOG_I(PHY, "fp->nr_band=%d\n", fp->nr_band);
  LOG_I(PHY, "fp->ofdm_offset_divisor=%d\n", fp->ofdm_offset_divisor);
  LOG_I(PHY, "fp->threequarter_fs=%d\n", fp->threequarter_fs);
  LOG_I(PHY, "fp->sl_CarrierFreq=%lu\n", fp->sl_CarrierFreq);
  LOG_I(PHY, "fp->N_RB_SL=%d\n", fp->N_RB_SL);
}

int nr_init_frame_parms_ue_sl(NR_DL_FRAME_PARMS *fp,
                              sl_nr_phy_config_request_t *config,
                              int threequarter_fs,
                              uint32_t ofdm_offset_divisor)
{
  // Set also these parameters here instead of some where else.
  fp->ofdm_offset_divisor = ofdm_offset_divisor;
  fp->threequarter_fs = threequarter_fs;

  fp->nr_band = get_band(config->sl_carrier_config.sl_frequency, 0);

  fp->att_rx = 0;
  fp->att_tx = 0;
  fp->nb_antennas_rx = config->sl_carrier_config.sl_num_rx_ant;
  fp->nb_antennas_tx = config->sl_carrier_config.sl_num_tx_ant;

  fp->numerology_index = config->sl_bwp_config.sl_scs;
  fp->N_RB_SL = config->sl_carrier_config.sl_grid_size;
  fp->N_RB_DL = fp->N_RB_SL;
  fp->N_RB_UL = fp->N_RB_SL;
  fp->Ncp = config->sl_bwp_config.sl_cyclic_prefix;

  fp->frame_type = get_frame_type(fp->nr_band, fp->numerology_index);
  int32_t uplink_frequency_offset = get_delta_duplex(fp->nr_band, fp->numerology_index);
  uplink_frequency_offset *= 1000;

  uint64_t bw_khz = (12 * config->sl_carrier_config.sl_grid_size) * (15 << config->sl_bwp_config.sl_scs);
  // REfer to section 3GPP spec 38.101 5.4E.2.1
  // FrefV2x = Fref + deltashift + valueN*5Khz
  uint32_t deltashift = (config->sl_carrier_config.sl_frequency_shift_7p5khz) ? 7500 : 0; // In Hz
  deltashift += config->sl_carrier_config.sl_value_N * 5000; // In Hz
  fp->sl_CarrierFreq = ((bw_khz >> 1) + config->sl_carrier_config.sl_frequency);
  fp->sl_CarrierFreq += (deltashift >> 1);
  fp->dl_CarrierFreq = fp->sl_CarrierFreq;
  fp->ul_CarrierFreq = fp->sl_CarrierFreq;

  LOG_D(PHY, "bw_kHz %lu, deltashift:%d Hz\n", bw_khz, deltashift);
  LOG_D(PHY, "CarrierFreq %lu Hz\n", fp->sl_CarrierFreq);

  LOG_I(PHY,
        "Initializing frame parms: DL frequency %lu Hz, UL frequency %lu Hz SL frequency %lu Hz: band %d, uldl offset %d Hz\n",
        fp->dl_CarrierFreq,
        fp->ul_CarrierFreq,
        fp->sl_CarrierFreq,
        fp->nr_band,
        uplink_frequency_offset);

  AssertFatal(fp->frame_type == TDD, "Sidelink bands only support TDD");

  AssertFatal(fp->ul_CarrierFreq == (fp->dl_CarrierFreq + uplink_frequency_offset),
              "Disagreement in uplink frequency for band %d: ul_CarrierFreq = %lu Hz vs expected %lu Hz\n",
              fp->nr_band,
              fp->ul_CarrierFreq,
              fp->dl_CarrierFreq + uplink_frequency_offset);

  LOG_I(PHY, "Initializing frame parms for mu %d, N_RB %d, Ncp %d\n", fp->numerology_index, fp->N_RB_DL, fp->Ncp);

  if (fp->Ncp == EXTENDED)
    AssertFatal(fp->numerology_index == NR_MU_2,
                "Invalid cyclic prefix %d for numerology index %d\n",
                fp->Ncp,
                fp->numerology_index);

  sl_set_scs_parameters(fp, fp->numerology_index, fp->N_RB_SL);

  fp->slots_per_frame = 10 * fp->slots_per_subframe;
  fp->symbols_per_slot = ((fp->Ncp == NORMAL) ? 14 : 12); // to redefine for different slot formats
  fp->samples_per_subframe_wCP = fp->ofdm_symbol_size * fp->symbols_per_slot * fp->slots_per_subframe;
  fp->samples_per_frame_wCP = 10 * fp->samples_per_subframe_wCP;
  fp->samples_per_slot_wCP = fp->symbols_per_slot * fp->ofdm_symbol_size;
  fp->samples_per_slotN0 = (fp->nb_prefix_samples + fp->ofdm_symbol_size) * fp->symbols_per_slot;
  fp->samples_per_slot0 =
      fp->nb_prefix_samples0 + ((fp->symbols_per_slot - 1) * fp->nb_prefix_samples) + (fp->symbols_per_slot * fp->ofdm_symbol_size);
  fp->samples_per_subframe = (fp->nb_prefix_samples0 + fp->ofdm_symbol_size) * 2
                             + (fp->nb_prefix_samples + fp->ofdm_symbol_size) * (fp->symbols_per_slot * fp->slots_per_subframe - 2);
  fp->get_samples_per_slot = &get_samples_per_slot;
  fp->get_samples_slot_timestamp = &get_samples_slot_timestamp;
  fp->samples_per_frame = 10 * fp->samples_per_subframe;
  fp->freq_range = get_freq_range_from_freq(fp->sl_CarrierFreq);

  // ssb_offset_pointa points to the first RE where Sidelink-PSBCH starts
  fp->ssb_start_subcarrier = config->sl_bwp_config.sl_ssb_offset_point_a;

  perform_symbol_rotation(fp, fp->sl_CarrierFreq, fp->symbol_rotation[link_type_sl]);
  init_timeshift_rotation(fp);

  // Not used for Sidelink
  fp->Lmax = 0;
  fp->L_ssb = 0;
  fp->N_ssb = 0;
  fp->half_frame_bit = 0;
  fp->ssb_index = 0;
  fp->ssb_type = 0;

  LOG_I(PHY, "Dumping Sidelink Frame Parameters\n");
  nr_dump_frame_parms(fp);
  return 0;
}
