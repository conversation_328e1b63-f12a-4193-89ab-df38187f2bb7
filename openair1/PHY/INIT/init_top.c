/*
   Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The OpenAirInterface Software Alliance licenses this file to You under
   the OAI Public License, Version 1.1  (the "License"); you may not use this file
   except in compliance with the License.
   You may obtain a copy of the License at

        http://www.openairinterface.org/?page_id=698

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
  -------------------------------------------------------------------------------
   For more information about the OpenAirInterface (OAI) Software Alliance:
        <EMAIL>
*/

/* 中文说明：本文件是LTE物理层初始化和重配置的核心文件
 * 主要功能：
 * 1. 初始化LTE物理层的基础算法库和查找表
 * 2. 生成调制解调查找表（QPSK、16QAM、64QAM）
 * 3. 初始化编码解码相关的库和参数
 * 4. 配置上行参考信号序列
 *
 * 数据流向：
 * 输入数据来源：
 * - LTE帧参数配置（LTE_DL_FRAME_PARMS）
 * - 系统初始化参数
 *
 * 输出数据流向：
 * - 初始化的查找表供调制解调使用
 * - 配置好的编码库供信道编码使用
 * - 生成的参考信号序列供信道估计使用
 *
 * 调用关系：
 * - 被LTE系统初始化函数调用
 * - 为LTE物理层处理提供基础支持
 * - 与NR初始化函数并行工作
 */

/*!\brief LTE PHY的初始化和重配置例程 */
#include "phy_init.h"                                    // PHY初始化头文件
#include "PHY/phy_extern.h"                             // PHY外部变量定义
#include "PHY/CODING/coding_extern.h"                   // 编码外部接口
#include "PHY/LTE_ESTIMATION/lte_estimation.h"          // LTE信道估计
#include "PHY/LTE_REFSIG/lte_refsig.h"                  // LTE参考信号
#include "PHY/LTE_TRANSPORT/transport_common_proto.h"    // LTE传输层通用协议
#include "openair1/PHY/LTE_TRANSPORT/transport_vars.h"   // LTE传输层变量

// 调制查找表全局变量
int qam64_table[8], qam16_table[4], qpsk_table[2];
void init_sss(void);  // SSS初始化函数声明

/* 中文说明：generate_64qam_table - 生成64QAM调制查找表
 *
 * 函数作用：
 * - 生成64QAM调制的星座点映射表
 * - 将3比特输入映射到复数星座点
 * - 用于快速64QAM调制处理
 *
 * 数据处理：
 * - 遍历所有可能的3比特组合（a,b,c）
 * - 计算对应的星座点幅度值
 * - 存储到全局查找表qam64_table中
 *
 * 输出数据流向：
 * - qam64_table供64QAM调制函数使用
 * - 用于PDSCH、PUSCH等信道的调制处理
 */
void generate_64qam_table(void) {
  int a,b,c,index;

  // 遍历所有3比特组合生成64QAM星座点
  for (a=-1; a<=1; a+=2)      // 最高位：-1或+1
    for (b=-1; b<=1; b+=2)    // 中间位：-1或+1
      for (c=-1; c<=1; c+=2) {// 最低位：-1或+1
        // 计算查找表索引
        index = (1+a)*2 + (1+b) + (1+c)/2;
        // 计算星座点幅度：使用Gray编码映射
        qam64_table[index] = -a*(QAM64_n1 + b*(QAM64_n2 + (c*QAM64_n3)));
      }
}

/* 中文说明：generate_16qam_table - 生成16QAM调制查找表
 *
 * 函数作用：
 * - 生成16QAM调制的星座点映射表
 * - 将2比特输入映射到复数星座点
 * - 用于快速16QAM调制处理
 *
 * 数据处理：
 * - 遍历所有可能的2比特组合（a,b）
 * - 计算对应的星座点幅度值
 * - 存储到全局查找表qam16_table中
 */
void generate_16qam_table(void) {
  int a,b,index;

  // 遍历所有2比特组合生成16QAM星座点
  for (a=-1; a<=1; a+=2)      // 高位：-1或+1
    for (b=-1; b<=1; b+=2) {  // 低位：-1或+1
      // 计算查找表索引
      index = (1+a) + (1+b)/2;
      // 计算星座点幅度：使用Gray编码映射
      qam16_table[index] = -a*(QAM16_n1 + (b*QAM16_n2));
    }
}

/* 中文说明：generate_qpsk_table - 生成QPSK调制查找表
 *
 * 函数作用：
 * - 生成QPSK调制的星座点映射表
 * - 将1比特输入映射到复数星座点
 * - 用于快速QPSK调制处理
 *
 * 数据处理：
 * - 遍历所有可能的1比特值（a）
 * - 计算对应的星座点幅度值
 * - 存储到全局查找表qpsk_table中
 */
void generate_qpsk_table(void) {
  int a,index;

  // 遍历所有1比特值生成QPSK星座点
  for (a=-1; a<=1; a+=2) {    // 比特值：-1或+1
    // 计算查找表索引
    index = (1+a)/2;
    // 计算星座点幅度
    qpsk_table[index] = -a*QPSK;
  }
}

/* 中文说明：init_lte_top - LTE物理层顶层初始化函数
 *
 * 函数作用：
 * - 初始化LTE物理层的所有基础组件和算法库
 * - 生成各种查找表和参考信号序列
 * - 加载编码解码库和初始化相关参数
 * - 为LTE物理层处理做全面准备
 *
 * 输入数据：
 * - frame_parms：LTE帧参数结构指针
 *
 * 数据处理：
 * - 初始化卷积编码器
 * - 生成Viterbi解码表
 * - 加载编码库
 * - 生成上行参考信号序列
 * - 生成调制查找表
 * - 初始化加扰/解扰查找表
 *
 * 输出数据流向：
 * - 初始化的库和表供LTE物理层各模块使用
 * - 参考信号序列供信道估计使用
 * - 调制表供调制解调使用
 *
 * 调用关系：
 * - 被LTE系统初始化函数调用
 * - 调用各种子模块初始化函数
 * - 与free_lte_top成对使用
 */
void init_lte_top(LTE_DL_FRAME_PARMS *frame_parms) {
  ccodelte_init();                    // 初始化LTE卷积编码器
  phy_generate_viterbi_tables_lte();  // 生成LTE Viterbi解码表
  load_codinglib();                   // 加载编码库
  generate_ul_ref_sigs();             // 生成上行参考信号序列
  generate_ul_ref_sigs_rx();          // 生成上行参考信号接收序列
  generate_64qam_table();             // 生成64QAM调制表
  generate_16qam_table();             // 生成16QAM调制表
  generate_qpsk_table();              // 生成QPSK调制表
  generate_RIV_tables();              // 生成RIV（资源指示值）表
  init_unscrambling_lut();            // 初始化解扰查找表
  init_scrambling_lut();              // 初始化加扰查找表
  //set_taus_seed(1328);              // 设置随机数种子（已注释）
}

/* 中文说明：free_lte_top - LTE物理层顶层资源释放函数
 *
 * 函数作用：
 * - 释放init_lte_top中分配的资源
 * - 清理编码库和相关内存
 * - 确保LTE系统关闭时没有内存泄漏
 *
 * 数据处理：
 * - 释放编码库资源
 * - 其他资源由相应的模块释放函数处理
 *
 * 调用关系：
 * - 被LTE系统关闭函数调用
 * - 与init_lte_top成对使用
 * - 注：上行参考信号的释放在phy_free_lte_eNB()中处理
 */
void free_lte_top(void) {
  free_codinglib();  // 释放编码库资源
  /* 注：free_ul_ref_sigs()在phy_free_lte_eNB()中调用 */
}


/*
 * @}*/
