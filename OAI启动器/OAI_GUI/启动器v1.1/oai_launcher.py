import sys
import subprocess
from PyQt5.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QMessageBox, QTextEdit, QLabel, QStatusBar, QDialog, QLineEdit, QListWidget, QListWidgetItem, QTabWidget, QCheckBox, QFileDialog, QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QTextCursor
import os

class LogTailThread(QThread):
    new_line = pyqtSignal(str)
    def __init__(self, log_path):
        super().__init__()
        self.log_path = os.path.expanduser(log_path)
        self._running = True

    def run(self):
        try:
            proc = subprocess.Popen(['tail', '-F', self.log_path], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True)
            while self._running:
                line = proc.stdout.readline()
                if not line:
                    break
                self.new_line.emit(line)
            proc.terminate()
        except Exception as e:
            self.new_line.emit(f"[错误] 无法读取日志: {e}\n")

    def stop(self):
        self._running = False
        self.terminate()

class RealTimeLogDialog(QDialog):
    def __init__(self, log_path, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 800, 400)
        vbox = QVBoxLayout()
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        vbox.addWidget(self.text)
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)
        self.setLayout(vbox)
        self.thread = LogTailThread(log_path)
        self.thread.new_line.connect(self.append_text)
        self.thread.start()

    def append_text(self, line):
        self.text.moveCursor(QTextCursor.End)
        self.text.insertPlainText(line)
        self.text.moveCursor(QTextCursor.End)

    def closeEvent(self, event):
        self.thread.stop()
        event.accept()

class MonitorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('运行监控')
        self.setGeometry(600, 350, 350, 180)
        vbox = QVBoxLayout()
        label = QLabel('请选择要查看的运行情况：')
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; margin-bottom: 10px;")
        vbox.addWidget(label)
        btn_gnb = QPushButton('OAI gNB运行情况')
        btn_gnb.setStyleSheet("font-size: 15px; padding: 8px; background-color: #00a8ff; color: white; border-radius: 8px;")
        btn_gnb.clicked.connect(lambda: self.open_multiple_logs(['gNB输出日志', 'gNB系统日志'], ['/tmp/gnb_output.log', '/tmp/openair.log'], 'OAI gNB运行情况'))
        btn_cn5g = QPushButton('CN5G运行情况')
        btn_cn5g.setStyleSheet("font-size: 15px; padding: 8px; background-color: #0097e6; color: white; border-radius: 8px;")
        btn_cn5g.clicked.connect(lambda: self.open_docker_log('oai-amf', 'CN5G AMF运行情况'))
        btn_nrue = QPushButton('nrUE运行情况')
        btn_nrue.setStyleSheet("font-size: 15px; padding: 8px; background-color: #9c88ff; color: white; border-radius: 8px;")
        btn_nrue.clicked.connect(lambda: self.open_multiple_logs(['nrUE输出日志', 'nrUE MAC日志'], ['/tmp/nrue_output.log', '/tmp/mac.log'], 'nrUE运行情况'))
        vbox.addWidget(btn_gnb)
        vbox.addWidget(btn_cn5g)
        vbox.addWidget(btn_nrue)
        self.setLayout(vbox)

    def open_log(self, log_path, title):
        dlg = RealTimeLogDialog(log_path, title)
        dlg.exec_()

    def open_docker_log(self, container_name, title):
        dlg = DockerLogDialog(container_name, title)
        dlg.exec_()

    def open_multiple_logs(self, log_names, log_paths, title):
        dlg = MultipleLogDialog(log_names, log_paths, title)
        dlg.exec_()

class MultipleLogDialog(QDialog):
    def __init__(self, log_names, log_paths, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 900, 500)

        vbox = QVBoxLayout()

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 为每个日志文件创建一个标签页
        self.log_widgets = []
        self.log_threads = []

        for i, (log_name, log_path) in enumerate(zip(log_names, log_paths)):
            # 创建文本显示区域
            text_widget = QTextEdit()
            text_widget.setReadOnly(True)
            self.log_widgets.append(text_widget)

            # 添加到标签页
            self.tab_widget.addTab(text_widget, log_name)

            # 创建日志监控线程
            if os.path.exists(log_path):
                thread = LogTailThread(log_path)
                thread.new_line.connect(lambda line, idx=i: self.append_text(line, idx))
                thread.start()
                self.log_threads.append(thread)
            else:
                # 如果文件不存在，显示提示信息
                text_widget.setText(f"日志文件 {log_path} 不存在或尚未生成。\n请先启动相应的服务。")
                self.log_threads.append(None)

        vbox.addWidget(self.tab_widget)

        # 关闭按钮
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)

        self.setLayout(vbox)

    def append_text(self, line, widget_index):
        if widget_index < len(self.log_widgets):
            widget = self.log_widgets[widget_index]
            widget.moveCursor(QTextCursor.End)
            widget.insertPlainText(line)
            widget.moveCursor(QTextCursor.End)

    def closeEvent(self, event):
        # 停止所有日志监控线程
        for thread in self.log_threads:
            if thread:
                thread.stop()
        event.accept()

class StaticMultipleLogDialog(QDialog):
    def __init__(self, log_names, log_paths, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 900, 500)

        vbox = QVBoxLayout()

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 为每个日志文件创建一个标签页
        for log_name, log_path in zip(log_names, log_paths):
            text_widget = QTextEdit()
            text_widget.setReadOnly(True)

            # 读取日志文件内容
            if os.path.exists(log_path):
                try:
                    # 显示最后100行
                    output = subprocess.check_output(['tail', '-n', '100', log_path],
                                                   universal_newlines=True)
                    text_widget.setText(output)
                except Exception as e:
                    text_widget.setText(f"无法读取日志文件 {log_path}: {e}")
            else:
                text_widget.setText(f"日志文件 {log_path} 不存在或尚未生成。\n请先启动相应的服务。")

            # 添加到标签页
            self.tab_widget.addTab(text_widget, log_name)

        vbox.addWidget(self.tab_widget)

        # 刷新和关闭按钮
        btn_layout = QHBoxLayout()
        refresh_btn = QPushButton('刷新')
        refresh_btn.clicked.connect(self.refresh_logs)
        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(self.close)
        btn_layout.addWidget(refresh_btn)
        btn_layout.addWidget(close_btn)
        vbox.addLayout(btn_layout)

        self.setLayout(vbox)

        # 保存参数以便刷新
        self.log_names = log_names
        self.log_paths = log_paths

    def refresh_logs(self):
        """刷新所有日志内容"""
        for i, log_path in enumerate(self.log_paths):
            text_widget = self.tab_widget.widget(i)
            if os.path.exists(log_path):
                try:
                    output = subprocess.check_output(['tail', '-n', '100', log_path],
                                                   universal_newlines=True)
                    text_widget.setText(output)
                except Exception as e:
                    text_widget.setText(f"无法读取日志文件 {log_path}: {e}")
            else:
                text_widget.setText(f"日志文件 {log_path} 不存在或尚未生成。\n请先启动相应的服务。")

class DockerLogDialog(QDialog):
    def __init__(self, container_name, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 800, 400)
        vbox = QVBoxLayout()
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        vbox.addWidget(self.text)
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)
        self.setLayout(vbox)
        self.container_name = container_name
        self.thread = DockerLogThread(container_name)
        self.thread.new_line.connect(self.append_text)
        self.thread.start()

    def append_text(self, line):
        self.text.moveCursor(QTextCursor.End)
        self.text.insertPlainText(line)
        self.text.moveCursor(QTextCursor.End)

    def closeEvent(self, event):
        self.thread.stop()
        event.accept()

class DockerLogThread(QThread):
    new_line = pyqtSignal(str)
    def __init__(self, container_name):
        super().__init__()
        self.container_name = container_name
        self._running = True

    def run(self):
        try:
            proc = subprocess.Popen(['docker', 'logs', '-f', self.container_name],
                                  stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                  universal_newlines=True)
            while self._running:
                line = proc.stdout.readline()
                if not line:
                    break
                self.new_line.emit(line)
            proc.terminate()
        except Exception as e:
            self.new_line.emit(f"[错误] 无法读取Docker日志: {e}\n")

    def stop(self):
        self._running = False
        self.terminate()

class CustomSearchDialog(QDialog):
    def __init__(self, filename, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f'搜索文件: {filename}')
        self.setGeometry(600, 350, 800, 500)

        vbox = QVBoxLayout()

        # 搜索信息
        info_label = QLabel(f'正在搜索文件: {filename}')
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 10px;")
        vbox.addWidget(info_label)

        # 搜索选项
        options_hbox = QHBoxLayout()
        self.search_path_edit = QLineEdit()
        self.search_path_edit.setText('~/')
        self.search_path_edit.setPlaceholderText('搜索起始路径（默认从用户主目录开始）')

        self.case_sensitive_cb = QCheckBox('区分大小写')
        self.include_hidden_cb = QCheckBox('包含隐藏文件')

        search_btn = QPushButton('开始搜索')
        search_btn.clicked.connect(lambda: self.start_search(filename))

        options_hbox.addWidget(QLabel('搜索路径:'))
        options_hbox.addWidget(self.search_path_edit)
        options_hbox.addWidget(self.case_sensitive_cb)
        options_hbox.addWidget(self.include_hidden_cb)
        options_hbox.addWidget(search_btn)
        vbox.addLayout(options_hbox)

        # 结果列表
        self.result_list = QListWidget()
        vbox.addWidget(self.result_list)

        # 状态标签
        self.status_label = QLabel('点击"开始搜索"来查找文件')
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        vbox.addWidget(self.status_label)

        # 按钮
        btn_hbox = QHBoxLayout()
        self.select_btn = QPushButton('选择')
        self.select_btn.setEnabled(False)
        self.select_btn.clicked.connect(self.accept)

        cancel_btn = QPushButton('取消')
        cancel_btn.clicked.connect(self.reject)

        btn_hbox.addWidget(self.select_btn)
        btn_hbox.addWidget(cancel_btn)
        vbox.addLayout(btn_hbox)

        self.setLayout(vbox)

        self.selected_path = None
        self.result_list.itemClicked.connect(self.on_item_clicked)
        self.result_list.itemDoubleClicked.connect(self.on_item_double_clicked)

    def start_search(self, filename):
        self.status_label.setText('搜索中...')
        self.result_list.clear()
        self.select_btn.setEnabled(False)

        search_path = self.search_path_edit.text().strip() or '~/'

        # 构建find命令
        find_cmd = f"find {search_path} -type f"

        if not self.include_hidden_cb.isChecked():
            find_cmd += " -not -path '*/.*'"

        if self.case_sensitive_cb.isChecked():
            find_cmd += f" -name '{filename}'"
        else:
            find_cmd += f" -iname '{filename}'"

        find_cmd += " 2>/dev/null"  # 忽略权限错误

        try:
            output = subprocess.check_output(find_cmd, shell=True, universal_newlines=True)
            paths = [line.strip() for line in output.split('\n') if line.strip()]

            if paths:
                for path in paths:
                    item = QListWidgetItem(path)
                    self.result_list.addItem(item)
                self.status_label.setText(f'找到 {len(paths)} 个匹配的文件')
            else:
                self.status_label.setText('未找到匹配的文件')

        except subprocess.CalledProcessError:
            self.status_label.setText('搜索失败，请检查路径和权限')
        except Exception as e:
            self.status_label.setText(f'搜索出错: {e}')

    def on_item_clicked(self, item):
        self.selected_path = item.text()
        self.select_btn.setEnabled(True)

    def on_item_double_clicked(self, item):
        self.selected_path = item.text()
        self.accept()

class PathSelectDialog(QDialog):
    def __init__(self, find_cmd, title, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setGeometry(600, 350, 700, 400)
        vbox = QVBoxLayout()
        self.list = QListWidget()
        vbox.addWidget(self.list)
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)
        self.setLayout(vbox)
        self.selected_path = None
        self.list.itemClicked.connect(self.on_item_clicked)
        self.populate(find_cmd)

    def populate(self, find_cmd):
        try:
            output = subprocess.check_output(find_cmd, shell=True, universal_newlines=True)
            paths = [line.strip() for line in output.split('\n') if line.strip()]
            for p in paths:
                item = QListWidgetItem(p)
                self.list.addItem(item)
        except Exception as e:
            self.list.addItem(f"[错误] 查找失败: {e}")

    def on_item_clicked(self, item):
        self.selected_path = item.text()
        self.accept()

class Launcher(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        # 新增：保存路径变量
        self.gnb_exec_path = ''
        self.gnb_conf_path = ''
        self.nrue_exec_path = ''
        self.nrue_conf_path = ''
        self.cn5g_path = ''
        self.custom_file_path = ''

        # 启动时加载脚本中的当前路径
        self.load_current_script_paths()

    def initUI(self):
        self.setWindowTitle('OAI 一键启动与监控平台')
        self.setGeometry(400, 200, 700, 700)
        self.setStyleSheet("background-color: #f5f6fa;")

        title = QLabel('OAI 一键启动与监控平台', self)
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 22px; font-weight: bold; margin-bottom: 18px;")

        # 路径配置组
        path_group = QGroupBox("📁 路径配置")
        path_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        path_layout = QGridLayout()

        # gNB 目录 (GNB_DIR)
        path_layout.addWidget(QLabel("gNB目录:"), 0, 0)
        self.gnb_dir_edit = QLineEdit()
        self.gnb_dir_edit.setPlaceholderText('gNB/nr-softmodem 所在目录')
        gnb_dir_btn = QPushButton('📁 浏览')
        gnb_dir_btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px; border-radius: 4px;")
        gnb_dir_btn.clicked.connect(self.browse_gnb_dir)
        path_layout.addWidget(self.gnb_dir_edit, 0, 1)
        path_layout.addWidget(gnb_dir_btn, 0, 2)

        # UE 目录 (UE_DIR)
        path_layout.addWidget(QLabel("UE目录:"), 1, 0)
        self.ue_dir_edit = QLineEdit()
        self.ue_dir_edit.setPlaceholderText('nr-uesoftmodem 所在目录')
        ue_dir_btn = QPushButton('📁 浏览')
        ue_dir_btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px; border-radius: 4px;")
        ue_dir_btn.clicked.connect(self.browse_ue_dir)
        path_layout.addWidget(self.ue_dir_edit, 1, 1)
        path_layout.addWidget(ue_dir_btn, 1, 2)

        # CN5G 目录 (CN_DIR)
        path_layout.addWidget(QLabel("CN5G目录:"), 2, 0)
        self.cn_dir_edit = QLineEdit()
        self.cn_dir_edit.setPlaceholderText('OAI CN5G 核心网目录')
        cn_dir_btn = QPushButton('📁 浏览')
        cn_dir_btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px; border-radius: 4px;")
        cn_dir_btn.clicked.connect(self.browse_cn_dir)
        path_layout.addWidget(self.cn_dir_edit, 2, 1)
        path_layout.addWidget(cn_dir_btn, 2, 2)

        # gNB 配置文件 (GNB_CONF)
        path_layout.addWidget(QLabel("gNB配置:"), 3, 0)
        self.gnb_conf_edit = QLineEdit()
        self.gnb_conf_edit.setPlaceholderText('gNB 配置文件路径')
        gnb_conf_btn = QPushButton('📄 浏览')
        gnb_conf_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 5px; border-radius: 4px;")
        gnb_conf_btn.clicked.connect(self.browse_gnb_conf)
        path_layout.addWidget(self.gnb_conf_edit, 3, 1)
        path_layout.addWidget(gnb_conf_btn, 3, 2)

        # UE 配置文件 (UE_CONF)
        path_layout.addWidget(QLabel("UE配置:"), 4, 0)
        self.ue_conf_edit = QLineEdit()
        self.ue_conf_edit.setPlaceholderText('UE 配置文件路径')
        ue_conf_btn = QPushButton('📄 浏览')
        ue_conf_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 5px; border-radius: 4px;")
        ue_conf_btn.clicked.connect(self.browse_ue_conf)
        path_layout.addWidget(self.ue_conf_edit, 4, 1)
        path_layout.addWidget(ue_conf_btn, 4, 2)

        path_group.setLayout(path_layout)

        # 自定义文件搜索
        custom_hbox = QHBoxLayout()
        self.custom_filename_edit = QLineEdit()
        self.custom_filename_edit.setPlaceholderText('输入要搜索的文件名（如：my_config.conf）')
        self.custom_result_edit = QLineEdit()
        self.custom_result_edit.setPlaceholderText('搜索结果将显示在这里')
        self.custom_result_edit.setReadOnly(True)
        custom_search_btn = QPushButton('搜索文件')
        custom_search_btn.clicked.connect(self.custom_file_search)
        custom_hbox.addWidget(QLabel('自定义搜索:'))
        custom_hbox.addWidget(self.custom_filename_edit)
        custom_hbox.addWidget(custom_search_btn)

        custom_result_hbox = QHBoxLayout()
        custom_result_hbox.addWidget(QLabel('搜索结果:'))
        custom_result_hbox.addWidget(self.custom_result_edit)

        # 按钮
        self.update_paths_btn = QPushButton('🔄 更新脚本路径')
        self.update_paths_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #ffa502; color: white; border-radius: 8px;")
        self.update_paths_btn.clicked.connect(self.update_script_paths)

        self.start_btn = QPushButton('🚀 开始运行')
        self.start_btn.setStyleSheet("font-size: 16px; padding: 10px; background-color: #4cd137; color: white; border-radius: 8px;")
        self.start_btn.clicked.connect(self.run_script)

        self.gnb_log_btn = QPushButton('📄 查看gNB日志')
        self.gnb_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #00a8ff; color: white; border-radius: 8px;")
        self.gnb_log_btn.clicked.connect(lambda: self.show_multiple_logs(['gNB输出日志', 'gNB系统日志'], ['/tmp/gnb_output.log', '/tmp/openair.log'], 'gNB 日志'))

        self.cn5g_log_btn = QPushButton('📄 查看CN5G日志')
        self.cn5g_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #0097e6; color: white; border-radius: 8px;")
        self.cn5g_log_btn.clicked.connect(lambda: self.show_docker_log('oai-amf', 'CN5G AMF日志'))

        self.nrue_log_btn = QPushButton('📄 查看nrUE日志')
        self.nrue_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #9c88ff; color: white; border-radius: 8px;")
        self.nrue_log_btn.clicked.connect(lambda: self.show_multiple_logs(['nrUE输出日志', 'nrUE MAC日志'], ['/tmp/nrue_output.log', '/tmp/mac.log'], 'nrUE 日志'))

        self.exit_btn = QPushButton('❌ 退出')
        self.exit_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #e84118; color: white; border-radius: 8px;")
        self.exit_btn.clicked.connect(self.close)

        # 布局
        vbox = QVBoxLayout()
        vbox.addWidget(title)
        vbox.addSpacing(10)

        # 添加路径配置组
        vbox.addWidget(path_group)
        vbox.addSpacing(5)

        # 保留自定义搜索功能
        vbox.addLayout(custom_hbox)
        vbox.addLayout(custom_result_hbox)
        vbox.addSpacing(10)

        # 按钮区域
        vbox.addWidget(self.update_paths_btn)
        vbox.addWidget(self.start_btn)
        vbox.addSpacing(10)
        vbox.addWidget(self.gnb_log_btn)
        vbox.addWidget(self.cn5g_log_btn)
        vbox.addWidget(self.nrue_log_btn)
        vbox.addSpacing(10)
        vbox.addWidget(self.exit_btn)
        vbox.addStretch(1)

        # 状态栏
        self.status = QStatusBar()
        self.status.showMessage('准备就绪')
        vbox.addWidget(self.status)

        self.setLayout(vbox)

    def run_script(self):
        try:
            # 在启动脚本之前，先更新路径
            self.update_script_paths()

            script_path = os.path.expanduser('./start_oai_all.sh')
            subprocess.Popen(['bash', script_path])
            self.status.showMessage('脚本已开始执行，请查看新终端窗口。')
            QMessageBox.information(self, "提示", "脚本已开始执行，请查看新终端窗口。\n\n你可以点击下方“运行情况”按钮实时查看各模块日志。")
            self.open_monitor_dialog()
        except Exception as e:
            self.status.showMessage('启动失败')
            QMessageBox.critical(self, "错误", f"启动失败: {e}")

    def open_monitor_dialog(self):
        dlg = MonitorDialog(self)
        dlg.exec_()

    def show_log(self, log_path, title):
        log_path = os.path.expanduser(log_path)
        if not os.path.exists(log_path):
            QMessageBox.warning(self, "日志不存在", f"日志文件 {log_path} 不存在！")
            return
        try:
            # 只显示最后50行
            output = subprocess.check_output(['tail', '-n', '50', log_path], universal_newlines=True)
        except Exception as e:
            output = f"无法读取日志: {e}"
        dlg = QMessageBox(self)
        dlg.setWindowTitle(title)
        dlg.setText(f"<pre>{output}</pre>")
        dlg.setStandardButtons(QMessageBox.Ok)
        dlg.setStyleSheet("QLabel{min-width: 600px; min-height: 300px;}")
        dlg.exec_()

    def show_multiple_logs(self, log_names, log_paths, title):
        """显示多个日志文件的静态内容"""
        dlg = StaticMultipleLogDialog(log_names, log_paths, title)
        dlg.exec_()

    def show_docker_log(self, container_name, title):
        """显示Docker容器日志"""
        try:
            output = subprocess.check_output(['docker', 'logs', '--tail', '50', container_name],
                                           universal_newlines=True, stderr=subprocess.STDOUT)
        except Exception as e:
            output = f"无法读取Docker日志: {e}"

        dlg = QMessageBox(self)
        dlg.setWindowTitle(title)
        dlg.setText(f"<pre>{output}</pre>")
        dlg.setStandardButtons(QMessageBox.Ok)
        dlg.setStyleSheet("QLabel{min-width: 600px; min-height: 300px;}")
        dlg.exec_()

    def browse_gnb_dir(self):
        """浏览选择gNB目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择gNB目录 (包含nr-softmodem的目录)",
            os.path.expanduser("~")
        )
        if directory:
            self.gnb_dir_edit.setText(directory)
            self.update_script_paths()

    def browse_ue_dir(self):
        """浏览选择UE目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择UE目录 (包含nr-uesoftmodem的目录)",
            os.path.expanduser("~")
        )
        if directory:
            self.ue_dir_edit.setText(directory)
            self.update_script_paths()

    def browse_cn_dir(self):
        """浏览选择CN5G目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择CN5G目录 (OAI CN5G核心网目录)",
            os.path.expanduser("~")
        )
        if directory:
            self.cn_dir_edit.setText(directory)
            self.update_script_paths()

    def browse_gnb_conf(self):
        """浏览选择gNB配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择gNB配置文件",
            os.path.expanduser("~"),
            "配置文件 (*.conf);;所有文件 (*.*)"
        )
        if file_path:
            self.gnb_conf_edit.setText(file_path)
            self.update_script_paths()

    def browse_ue_conf(self):
        """浏览选择UE配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择UE配置文件",
            os.path.expanduser("~"),
            "配置文件 (*.conf);;所有文件 (*.*)"
        )
        if file_path:
            self.ue_conf_edit.setText(file_path)
            self.update_script_paths()

    def custom_file_search(self):
        """自定义文件搜索功能"""
        filename = self.custom_filename_edit.text().strip()
        if not filename:
            QMessageBox.warning(self, "输入错误", "请输入要搜索的文件名！")
            return

        dlg = CustomSearchDialog(filename, self)
        if dlg.exec_() == QDialog.Accepted and dlg.selected_path:
            self.custom_result_edit.setText(dlg.selected_path)
            self.custom_file_path = dlg.selected_path
            self.update_script_paths()
            QMessageBox.information(self, "搜索成功", f"已选择文件：\n{dlg.selected_path}")
        else:
            self.custom_result_edit.setText("未选择文件")

    def update_script_paths(self):
        """更新脚本文件中的路径变量"""
        try:
            script_path = './start_oai_all.sh'

            # 读取脚本文件
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 更新五个路径变量（从输入框获取值）
            gnb_dir = self.gnb_dir_edit.text().strip()
            if gnb_dir:
                content = self._update_script_variable(content, 'GNB_DIR', f'"{gnb_dir}"')
                print(f"更新 GNB_DIR 为: {gnb_dir}")

            ue_dir = self.ue_dir_edit.text().strip()
            if ue_dir:
                content = self._update_script_variable(content, 'UE_DIR', f'"{ue_dir}"')
                print(f"更新 UE_DIR 为: {ue_dir}")

            cn_dir = self.cn_dir_edit.text().strip()
            if cn_dir:
                content = self._update_script_variable(content, 'CN_DIR', f'"{cn_dir}"')
                print(f"更新 CN_DIR 为: {cn_dir}")

            gnb_conf = self.gnb_conf_edit.text().strip()
            if gnb_conf:
                content = self._update_script_variable(content, 'GNB_CONF', f'"{gnb_conf}"')
                print(f"更新 GNB_CONF 为: {gnb_conf}")

            ue_conf = self.ue_conf_edit.text().strip()
            if ue_conf:
                content = self._update_script_variable(content, 'UE_CONF', f'"{ue_conf}"')
                print(f"更新 UE_CONF 为: {ue_conf}")

            # 保留自定义文件功能
            if hasattr(self, 'custom_file_path') and self.custom_file_path:
                content = self._update_script_variable(content, 'CUSTOM_FILE', f'"{self.custom_file_path}"')
                print(f"更新 CUSTOM_FILE 为: {self.custom_file_path}")

            # 写回脚本文件
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)

            self.status.showMessage('脚本路径已更新')

            # 显示更新的路径信息
            updated_paths = []
            if gnb_dir: updated_paths.append(f"GNB_DIR: {gnb_dir}")
            if ue_dir: updated_paths.append(f"UE_DIR: {ue_dir}")
            if cn_dir: updated_paths.append(f"CN_DIR: {cn_dir}")
            if gnb_conf: updated_paths.append(f"GNB_CONF: {gnb_conf}")
            if ue_conf: updated_paths.append(f"UE_CONF: {ue_conf}")

            if updated_paths:
                QMessageBox.information(self, "更新成功",
                    f"脚本文件中的路径已更新！\n\n更新的路径:\n" + "\n".join(updated_paths))
            else:
                QMessageBox.information(self, "提示", "没有检测到路径更改")

        except Exception as e:
            self.status.showMessage('更新失败')
            QMessageBox.warning(self, "更新失败", f"更新脚本路径失败: {e}")

    def _update_script_variable(self, content, var_name, new_value):
        """更新脚本中的变量值"""
        import re
        pattern = rf'^{var_name}=.*$'
        replacement = f'{var_name}={new_value}'
        return re.sub(pattern, replacement, content, flags=re.MULTILINE)

    def load_current_script_paths(self):
        """从脚本文件中加载当前的路径设置"""
        try:
            script_path = './start_oai_all.sh'
            if not os.path.exists(script_path):
                return

            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            import re

            # 提取各个路径变量的值
            def extract_var_value(var_name):
                pattern = rf'^{var_name}="([^"]*)"'
                match = re.search(pattern, content, re.MULTILINE)
                return match.group(1) if match else ""

            # 显示当前脚本中的路径（仅显示，不覆盖用户搜索的路径）
            current_gnb_dir = extract_var_value('GNB_DIR')
            current_ue_dir = extract_var_value('UE_DIR')
            current_cn_dir = extract_var_value('CN_DIR')
            current_gnb_conf = extract_var_value('GNB_CONF')
            current_ue_conf = extract_var_value('UE_CONF')
            current_custom_file = extract_var_value('CUSTOM_FILE')

            # 将脚本中的当前值加载到输入框中
            if current_gnb_dir:
                self.gnb_dir_edit.setText(current_gnb_dir)
            if current_ue_dir:
                self.ue_dir_edit.setText(current_ue_dir)
            if current_cn_dir:
                self.cn_dir_edit.setText(current_cn_dir)
            if current_gnb_conf:
                self.gnb_conf_edit.setText(current_gnb_conf)
            if current_ue_conf:
                self.ue_conf_edit.setText(current_ue_conf)

            # 保留自定义文件的显示
            if hasattr(self, 'custom_result_edit'):
                if current_custom_file:
                    self.custom_result_edit.setPlaceholderText(f'当前自定义文件: {current_custom_file}')
                else:
                    self.custom_result_edit.setPlaceholderText('尚未设置自定义文件')

        except Exception as e:
            print(f"加载脚本路径失败: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = Launcher()
    ex.show()
    sys.exit(app.exec_()) 

