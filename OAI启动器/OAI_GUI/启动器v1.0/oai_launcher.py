import sys
import subprocess
from PyQt5.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QMessageBox, QTextEdit, QLabel, QStatusBar, QDialog
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QTextCursor
import os

class LogTailThread(QThread):
    new_line = pyqtSignal(str)
    def __init__(self, log_path):
        super().__init__()
        self.log_path = os.path.expanduser(log_path)
        self._running = True

    def run(self):
        try:
            proc = subprocess.Popen(['tail', '-F', self.log_path], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True)
            while self._running:
                line = proc.stdout.readline()
                if not line:
                    break
                self.new_line.emit(line)
            proc.terminate()
        except Exception as e:
            self.new_line.emit(f"[错误] 无法读取日志: {e}\n")

    def stop(self):
        self._running = False
        self.terminate()

class RealTimeLogDialog(QDialog):
    def __init__(self, log_path, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 800, 400)
        vbox = QVBoxLayout()
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        vbox.addWidget(self.text)
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)
        self.setLayout(vbox)
        self.thread = LogTailThread(log_path)
        self.thread.new_line.connect(self.append_text)
        self.thread.start()

    def append_text(self, line):
        self.text.moveCursor(QTextCursor.End)
        self.text.insertPlainText(line)
        self.text.moveCursor(QTextCursor.End)

    def closeEvent(self, event):
        self.thread.stop()
        event.accept()

class MonitorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('运行监控')
        self.setGeometry(600, 350, 350, 180)
        vbox = QVBoxLayout()
        label = QLabel('请选择要查看的运行情况：')
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; margin-bottom: 10px;")
        vbox.addWidget(label)
        btn_gnb = QPushButton('OAI gNB运行情况')
        btn_gnb.setStyleSheet("font-size: 15px; padding: 8px; background-color: #00a8ff; color: white; border-radius: 8px;")
        btn_gnb.clicked.connect(lambda: self.open_log('/tmp/openair.log', 'OAI gNB运行情况'))
        btn_cn5g = QPushButton('CN5G运行情况')
        btn_cn5g.setStyleSheet("font-size: 15px; padding: 8px; background-color: #0097e6; color: white; border-radius: 8px;")
        btn_cn5g.clicked.connect(lambda: self.open_docker_log('oai-amf', 'CN5G AMF运行情况'))
        btn_nrue = QPushButton('nrUE运行情况')
        btn_nrue.setStyleSheet("font-size: 15px; padding: 8px; background-color: #9c88ff; color: white; border-radius: 8px;")
        btn_nrue.clicked.connect(lambda: self.open_log('/tmp/mac.log', 'nrUE运行情况'))
        vbox.addWidget(btn_gnb)
        vbox.addWidget(btn_cn5g)
        vbox.addWidget(btn_nrue)
        self.setLayout(vbox)

    def open_log(self, log_path, title):
        dlg = RealTimeLogDialog(log_path, title)
        dlg.exec_()

    def open_docker_log(self, container_name, title):
        dlg = DockerLogDialog(container_name, title)
        dlg.exec_()

class DockerLogDialog(QDialog):
    def __init__(self, container_name, title):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(500, 300, 800, 400)
        vbox = QVBoxLayout()
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        vbox.addWidget(self.text)
        self.close_btn = QPushButton('关闭')
        self.close_btn.clicked.connect(self.close)
        vbox.addWidget(self.close_btn)
        self.setLayout(vbox)
        self.container_name = container_name
        self.thread = DockerLogThread(container_name)
        self.thread.new_line.connect(self.append_text)
        self.thread.start()

    def append_text(self, line):
        self.text.moveCursor(QTextCursor.End)
        self.text.insertPlainText(line)
        self.text.moveCursor(QTextCursor.End)

    def closeEvent(self, event):
        self.thread.stop()
        event.accept()

class DockerLogThread(QThread):
    new_line = pyqtSignal(str)
    def __init__(self, container_name):
        super().__init__()
        self.container_name = container_name
        self._running = True

    def run(self):
        try:
            proc = subprocess.Popen(['docker', 'logs', '-f', self.container_name],
                                  stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                  universal_newlines=True)
            while self._running:
                line = proc.stdout.readline()
                if not line:
                    break
                self.new_line.emit(line)
            proc.terminate()
        except Exception as e:
            self.new_line.emit(f"[错误] 无法读取Docker日志: {e}\n")

    def stop(self):
        self._running = False
        self.terminate()

class Launcher(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('OAI 一键启动与监控平台')
        self.setGeometry(400, 200, 420, 320)
        self.setStyleSheet("background-color: #f5f6fa;")

        # 标题
        title = QLabel('OAI 一键启动与监控平台', self)
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 22px; font-weight: bold; margin-bottom: 18px;")

        # 按钮
        self.start_btn = QPushButton('🚀 开始运行')
        self.start_btn.setStyleSheet("font-size: 16px; padding: 10px; background-color: #4cd137; color: white; border-radius: 8px;")
        self.start_btn.clicked.connect(self.run_script)

        self.gnb_log_btn = QPushButton('📄 查看gNB日志')
        self.gnb_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #00a8ff; color: white; border-radius: 8px;")
        self.gnb_log_btn.clicked.connect(lambda: self.show_log('~/gnb.log', 'gNB 日志'))

        self.cn5g_log_btn = QPushButton('📄 查看CN5G日志')
        self.cn5g_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #0097e6; color: white; border-radius: 8px;")
        self.cn5g_log_btn.clicked.connect(lambda: self.show_log('~/cn5g.log', 'CN5G 日志'))

        self.nrue_log_btn = QPushButton('📄 查看nrUE日志')
        self.nrue_log_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #9c88ff; color: white; border-radius: 8px;")
        self.nrue_log_btn.clicked.connect(lambda: self.show_log('~/nrue.log', 'nrUE 日志'))

        self.exit_btn = QPushButton('❌ 退出')
        self.exit_btn.setStyleSheet("font-size: 15px; padding: 8px; background-color: #e84118; color: white; border-radius: 8px;")
        self.exit_btn.clicked.connect(self.close)

        # 布局
        vbox = QVBoxLayout()
        vbox.addWidget(title)
        vbox.addSpacing(10)
        vbox.addWidget(self.start_btn)
        vbox.addSpacing(10)
        vbox.addWidget(self.gnb_log_btn)
        vbox.addWidget(self.cn5g_log_btn)
        vbox.addWidget(self.nrue_log_btn)
        vbox.addSpacing(10)
        vbox.addWidget(self.exit_btn)
        vbox.addStretch(1)

        # 状态栏
        self.status = QStatusBar()
        self.status.showMessage('准备就绪')
        vbox.addWidget(self.status)

        self.setLayout(vbox)

    def run_script(self):
        try:
            script_path = os.path.expanduser('~/start_oai_all.sh')
            subprocess.Popen(['bash', script_path])
            self.status.showMessage('脚本已开始执行，请查看新终端窗口。')
            QMessageBox.information(self, "提示", "脚本已开始执行，请查看新终端窗口。\n\n你可以点击下方“运行情况”按钮实时查看各模块日志。")
            self.open_monitor_dialog()
        except Exception as e:
            self.status.showMessage('启动失败')
            QMessageBox.critical(self, "错误", f"启动失败: {e}")

    def open_monitor_dialog(self):
        dlg = MonitorDialog(self)
        dlg.exec_()

    def show_log(self, log_path, title):
        log_path = os.path.expanduser(log_path)
        if not os.path.exists(log_path):
            QMessageBox.warning(self, "日志不存在", f"日志文件 {log_path} 不存在！")
            return
        try:
            # 只显示最后50行
            output = subprocess.check_output(['tail', '-n', '50', log_path], universal_newlines=True)
        except Exception as e:
            output = f"无法读取日志: {e}"
        dlg = QMessageBox(self)
        dlg.setWindowTitle(title)
        dlg.setText(f"<pre>{output}</pre>")
        dlg.setStandardButtons(QMessageBox.Ok)
        dlg.setStyleSheet("QLabel{min-width: 600px; min-height: 300px;}")
        dlg.exec_()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = Launcher()
    ex.show()
    sys.exit(app.exec_()) 
