#!/bin/bash

# =============================
# 一键启动 OAI gNB、CN5G、nrue 脚本
# =============================
# 请根据实际环境修改以下路径和参数

           

GNB_DIR="~/openairinterface5g/cmake_targets/ran_build/build"         # gNB/nr-softmodem 所在目录
UE_DIR="~/openairinterface5g/cmake_targets/ran_build/build"         # nr-uesoftmodem 所在目录
CN_DIR="$HOME/oai-cn5g"            # OAI CN5G 核心网目录
GNB_CONF="../../../targets/PROJECTS/GENERIC-NR-5GC/CONF/gnb.sa.band78.fr1.106PRB.usrpb210.conf"
UE_CONF="../../../targets/PROJECTS/GENERIC-NR-5GC/CONF/ue.conf"
GNB_IP="**************"            # gNB 网卡IP（用于核心网路由）

# 1. 启动 RF 模拟器 gNB
gnome-terminal -- bash -c "\
cd $GNB_DIR; \
echo '启动 gNB RF模拟器...'; \
echo "123" | sudo -S ./nr-softmodem -O $GNB_CONF --gNBs.[0].min_rxtxtime 6 --rfsim -d; \
exec bash\
"

# 2. 启动 OAI CN5G 核心网
gnome-terminal -- bash -c "\
cd $CN_DIR; \
echo '启动 OAI CN5G 核心网...'; \
docker compose up -d; \
echo "123" | sudo -S ip route add ********/24 via **************; \
echo '跟踪 oai-amf 日志...'; \
docker logs oai-amf -f; \
exec bash\
"

# 3. 启动 OAI nrue
gnome-terminal -- bash -c "\
cd $UE_DIR; \
echo '启动 OAI nrue...'; \
echo "123" | sudo -S ./nr-uesoftmodem --rfsim --rfsimulator.serveraddr 127.0.0.1 -r 106 --numerology 1 --band 78 -C 3619200000 -O $UE_CONF -d; \
exec bash\
"

echo "所有服务已在新终端窗口启动。"
